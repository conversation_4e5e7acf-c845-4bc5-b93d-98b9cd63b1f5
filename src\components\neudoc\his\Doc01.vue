<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { getReq, postReq } from '../../../utils/api'
import { ElMessage } from 'element-plus'

export default {
  setup() {
    const loading = ref(false)
    const isSaved = ref(false)
    const showPatientDialog = ref(false)
    const patientList = ref([])
    const medicalRecord = ref({
      readme: '',
      present: '',
      presentTreat: '',
      history: '',
      allergy: '',
      physique: ''
    })
    const medicalRecordId = ref('')
    const caseNumber = ref('') // 新增病历号
    const registId = ref('') // 新增挂号ID区分

    // 初步诊断相关数据
    const diagnosisType = ref('western') // 'western' 或 'chinese'
    const selectedWesternDiseases = ref([]) // 选中的西医疾病
    const selectedChineseDiseases = ref([]) // 选中的中医疾病
    const westernDiseases = ref([]) // 西医疾病列表
    const chineseDiseases = ref([]) // 中医疾病列表
    const diseaseSearchKeyword = ref('') // 疾病搜索关键词
    const showDiseaseDialog = ref(false) // 疾病选择对话框显示状态
    const currentDiseaseType = ref('') // 当前选择的疾病类型

    const handleSave = async () => {
      try {
        console.log('暂存开始 - medicalRecordId:', medicalRecordId.value)
        console.log('暂存开始 - registId:', registId.value)
        console.log('暂存开始 - caseNumber:', caseNumber.value)

        // 验证挂号ID是否有值
        if (!registId.value) {
          ElMessage.error('请先选择患者')
          return
        }

        // 验证必填字段
        if (!medicalRecord.value.readme) {
          ElMessage.error('主诉不能为空')
          return
        }

        // 构建完整的病历对象(字段名与后端实体类一致)
        const record = {
          registID: registId.value, // 使用挂号ID
          id: medicalRecordId.value || null, // 病历ID(更新时使用) - 使用小写id匹配后端实体类
          // 病历号由后端生成
          readme: medicalRecord.value.readme || '', // 主诉
          present: medicalRecord.value.present || '', // 现病史
          presentTreat: medicalRecord.value.presentTreat || '', // 现病治疗
          history: medicalRecord.value.history || '', // 既往史
          allergy: medicalRecord.value.allergy || '', // 过敏史
          physique: medicalRecord.value.physique || '', // 体格检查
          diagnosis: JSON.stringify({
            type: diagnosisType.value,
            western: selectedWesternDiseases.value,
            chinese: selectedChineseDiseases.value
          }), // 将诊断信息序列化存储到diagnosis字段
          caseState: 1 // 1表示暂存状态
        }
        
        // 调用后端接口
        console.log('提交参数:', JSON.stringify(record, null, 2))
        const result = await postReq('/neudoc/saveMedicalRecord', record)
        console.log('响应结果:', JSON.stringify(result.data, null, 2))
        
        if (result?.data?.result) {
          // 确保正确处理返回数据
          const responseData = result.data.data || {}
          medicalRecordId.value = responseData.ID || responseData.id || medicalRecordId.value
          isSaved.value = false // 暂存后允许继续编辑

          if (responseData.ID || responseData.id) {
            ElMessage.success('病历暂存成功')
            // 更新本地病历数据
            if (responseData.caseNumber) {
              caseNumber.value = responseData.caseNumber
            }
          } else {
            ElMessage.success('病历暂存成功(无返回ID)')
          }
        } else {
          const errorMsg = result?.data?.errMsg || '暂存失败'
          const errorDetails = result?.data?.errorDetails || ''
          console.error('暂存失败:', errorMsg, '详细信息:', errorDetails, '完整响应:', result)
          ElMessage.error(`${errorMsg}${errorDetails ? `: ${errorDetails}` : ''}`)
        }
      } catch (error) {
        ElMessage.error('操作失败: ' + error.message)
      }
    }

    const handleSubmit = async () => {
      try {
        loading.value = true
        
        // 验证必填字段
        if (!registId.value) {
          ElMessage.error('请先选择患者')
          return
        }
        if (!medicalRecord.value.readme) {
          ElMessage.error('主诉不能为空')
          return
        }

        // 验证患者数据
        console.log('当前registId:', registId.value)
        console.log('当前caseNumber:', caseNumber.value)
        
        if (!registId.value) {
          throw new Error('患者挂号ID数据缺失')
        }
        
        // 构建完整的病历对象
        const record = {
          registID: registId.value, // 使用已存储的挂号ID
          id: medicalRecordId.value || null, // 使用小写id字段匹配后端实体类
          caseNumber: caseNumber.value || '', // 使用已存储的病历号
          readme: medicalRecord.value.readme || '',
          present: medicalRecord.value.present || '',
          presentTreat: medicalRecord.value.presentTreat || '',
          history: medicalRecord.value.history || '',
          allergy: medicalRecord.value.allergy || '',
          physique: medicalRecord.value.physique || '',
          diagnosis: JSON.stringify({
            type: diagnosisType.value,
            western: selectedWesternDiseases.value,
            chinese: selectedChineseDiseases.value
          }), // 将诊断信息序列化存储到diagnosis字段
          caseState: 2 // 已提交状态
        }
        
        console.log('构建的record对象:', JSON.stringify(record, null, 2))
        console.log('当前medicalRecordId:', medicalRecordId.value)
        console.log('当前registId:', registId.value)

        console.log('提交参数:', JSON.stringify(record, null, 2))
        
        // 调用后端接口，设置10秒超时
        const result = await postReq('/neudoc/saveMedicalRecord', record)
        console.log('响应结果:', JSON.stringify(result.data, null, 2))
        
        if (result?.data?.result) {
          // 确保正确处理返回数据
          const responseData = result.data.data || {}
          medicalRecordId.value = responseData.ID || responseData.id || medicalRecordId.value
          isSaved.value = true // 提交后禁用编辑

          if (responseData.ID || responseData.id) {
            ElMessage.success('病历提交成功')
            // 更新本地病历数据
            if (responseData.caseNumber) {
              caseNumber.value = responseData.caseNumber
            }
            // 保存患者数据到sessionStorage
            const patientData = {
              ID: responseData.ID || responseData.id,
              CaseNumber: responseData.caseNumber || caseNumber.value,
              RegistID: registId.value,
              Readme: medicalRecord.value.readme,
              Present: medicalRecord.value.present,
              PresentTreat: medicalRecord.value.presentTreat,
              History: medicalRecord.value.history,
              Allergy: medicalRecord.value.allergy,
              Physique: medicalRecord.value.physique,
              DiagnosisType: diagnosisType.value,
              WesternDiagnosis: selectedWesternDiseases.value,
              ChineseDiagnosis: selectedChineseDiseases.value,
              CaseState: 2 // 已提交状态
            }
            sessionStorage.setItem('medicalRecord', JSON.stringify(patientData))
          } else {
            ElMessage.success('病历提交成功(无返回ID)')
            // 保存患者数据到sessionStorage
            const patientData = {
              ID: medicalRecordId.value,
              CaseNumber: caseNumber.value,
              RegistID: registId.value,
              Readme: medicalRecord.value.readme,
              Present: medicalRecord.value.present,
              PresentTreat: medicalRecord.value.presentTreat,
              History: medicalRecord.value.history,
              Allergy: medicalRecord.value.allergy,
              Physique: medicalRecord.value.physique,
              DiagnosisType: diagnosisType.value,
              WesternDiagnosis: selectedWesternDiseases.value,
              ChineseDiagnosis: selectedChineseDiseases.value,
              CaseState: 2 // 已提交状态
            }
            sessionStorage.setItem('medicalRecord', JSON.stringify(patientData))
          }
        } else {
          const errorMsg = result?.data?.errMsg || '提交失败'
          const errorDetails = result?.data?.errorDetails || ''
          console.error('提交失败:', errorMsg, '详细信息:', errorDetails, '完整响应:', result)
          ElMessage.error(`${errorMsg}${errorDetails ? `: ${errorDetails}` : ''}`)
        }
      } catch (error) {
        console.error('提交异常:', error)
        if (error.response) {
          // 请求已发出，服务器返回状态码不在2xx范围
          ElMessage.error(`服务器错误: ${error.response.status} ${error.response.statusText}`)
        } else if (error.request) {
          // 请求已发出但没有收到响应
          ElMessage.error('网络错误: 无法连接到服务器')
        } else {
          // 其他错误
          ElMessage.error('提交失败: ' + error.message)
        }
      } finally {
        loading.value = false
      }
    }

    const handleClear = () => {
      // 清空表单逻辑
      medicalRecord.value = {
        readme: '',
        present: '',
        presentTreat: '',
        history: '',
        allergy: '',
        physique: ''
      }
      medicalRecordId.value = ''
      registId.value = ''
      isSaved.value = false
      // 清空诊断数据
      diagnosisType.value = 'western'
      selectedWesternDiseases.value = []
      selectedChineseDiseases.value = []
      ElMessage.success('已清空所有内容')
    }

    const handleRefresh = async () => {
      // 刷新逻辑
      if (registId.value) {
        await loadMedicalRecord(registId.value)
      } else {
        handleClear()
      }
      ElMessage.success('刷新成功')
    }

    const handlePatientSelected = (event) => {
      const patient = event.detail
      console.log('收到患者数据:', patient)
      // 确保使用正确的字段：registID作为挂号ID，id作为患者ID
      registId.value = patient.registID || patient.id
      caseNumber.value = patient.caseNumber || ''
      console.log('设置后的registId:', registId.value)
      console.log('设置后的caseNumber:', caseNumber.value)
      loadMedicalRecord(patient.registID || patient.id)
    }

    onMounted(() => {
      console.log('组件已挂载')
      window.addEventListener('patient-selected', handlePatientSelected)
    })

    onUnmounted(() => {
      window.removeEventListener('patient-selected', handlePatientSelected)
    })

    //获取病人病历首页信息
    async function loadMedicalRecord(rid) {
      loading.value = true
      try {
        console.log('加载病历前caseNumber:', caseNumber.value)
        const response = await getReq(`/neudoc/getMedicalRecord?rid=${rid}`)
        console.log('loadMedicalRecord响应:', JSON.stringify(response.data, null, 2))
        if (response.data.result && response.data.data) {
          console.log('病历数据:', JSON.stringify(response.data.data, null, 2))
          medicalRecord.value = response.data.data
          // 设置病历ID，保留原始caseNumber除非返回的数据中有值
          const recordId = response.data.data.ID || response.data.data.id || ''
          console.log('提取的recordId:', recordId)
          medicalRecordId.value = recordId
          if (response.data.data.caseNumber) {
            console.log('从API获取的caseNumber:', response.data.data.caseNumber)
            // 只有当API返回的caseNumber与当前一致时才更新
            if (response.data.data.caseNumber === caseNumber.value) {
              caseNumber.value = response.data.data.caseNumber
            } else {
              console.warn('caseNumber不一致! 患者数据:', caseNumber.value, 'API返回:', response.data.data.caseNumber)
              // 保持原始caseNumber不变
            }
          }
          // 加载诊断数据
          if (response.data.data.diagnosis) {
            try {
              const diagnosisData = JSON.parse(response.data.data.diagnosis)
              if (diagnosisData.type) {
                diagnosisType.value = diagnosisData.type
              }
              if (diagnosisData.western) {
                selectedWesternDiseases.value = diagnosisData.western || []
              }
              if (diagnosisData.chinese) {
                selectedChineseDiseases.value = diagnosisData.chinese || []
              }
            } catch (error) {
              console.warn('解析诊断数据失败:', error)
              // 如果解析失败，保持默认值
            }
          }
          console.log('加载病历后caseNumber:', caseNumber.value)
        } else {
          // 无病历信息时初始化空表单，保留原始caseNumber
          medicalRecord.value = {
            readme: '',
            present: '',
            presentTreat: '',
            history: '',
            allergy: '',
            physique: ''
          }
          // 初始化诊断数据
          diagnosisType.value = 'western'
          selectedWesternDiseases.value = []
          selectedChineseDiseases.value = []
          ElMessage.info('该患者暂无历史病历信息')
        }
      } catch (err) {
        ElMessage.error('加载病历失败: ' + err.message)
      } finally {
        loading.value = false
      }
    }

    function selectPatient(row) {
      registId.value = row.id
      caseNumber.value = row.caseNumber || ''
      showPatientDialog.value = false
      loadMedicalRecord(row.id)
    }

    // 加载疾病数据
    const loadDiseases = async (type = 'all', keyword = '') => {
      try {
        let url = `/disease/page?count=100&pn=1`
        if (keyword) {
          url += `&keyword=${encodeURIComponent(keyword)}`
        }

        const result = await getReq(url)
        if (result.data.result) {
          const diseases = result.data.data.records || []

          // 根据疾病分类区分西医和中医疾病
          // 假设疾病分类中包含"西医"或"中医"关键词来区分
          if (type === 'western' || type === 'all') {
            westernDiseases.value = diseases.filter(disease =>
              disease.diseCategoryName &&
              (disease.diseCategoryName.includes('西医') ||
               disease.diseCategoryName.includes('内科') ||
               disease.diseCategoryName.includes('外科') ||
               disease.diseCategoryName.includes('妇科') ||
               disease.diseCategoryName.includes('儿科') ||
               !disease.diseCategoryName.includes('中医'))
            )
          }

          if (type === 'chinese' || type === 'all') {
            chineseDiseases.value = diseases.filter(disease =>
              disease.diseCategoryName &&
              disease.diseCategoryName.includes('中医')
            )
          }
        }
      } catch (error) {
        ElMessage.error('加载疾病数据失败: ' + error.message)
      }
    }

    // 打开疾病选择对话框
    const openDiseaseDialog = (type) => {
      currentDiseaseType.value = type
      showDiseaseDialog.value = true
      loadDiseases(type)
    }

    // 选择疾病
    const selectDisease = (disease) => {
      if (currentDiseaseType.value === 'western') {
        if (!selectedWesternDiseases.value.find(d => d.id === disease.id)) {
          selectedWesternDiseases.value.push(disease)
        }
      } else if (currentDiseaseType.value === 'chinese') {
        if (!selectedChineseDiseases.value.find(d => d.id === disease.id)) {
          selectedChineseDiseases.value.push(disease)
        }
      }
      showDiseaseDialog.value = false
    }

    // 移除选中的疾病
    const removeDisease = (disease, type) => {
      if (type === 'western') {
        selectedWesternDiseases.value = selectedWesternDiseases.value.filter(d => d.id !== disease.id)
      } else if (type === 'chinese') {
        selectedChineseDiseases.value = selectedChineseDiseases.value.filter(d => d.id !== disease.id)
      }
    }

    // 搜索疾病
    const searchDiseases = () => {
      loadDiseases(currentDiseaseType.value, diseaseSearchKeyword.value)
    }

    return {
      loading,
      isSaved,
      medicalRecord,
      medicalRecordId,
      caseNumber,
      showPatientDialog,
      patientList,
      handleSave,
      handleSubmit,
      handleClear,
      handleRefresh,
      selectPatient,
      // 初步诊断相关
      diagnosisType,
      selectedWesternDiseases,
      selectedChineseDiseases,
      westernDiseases,
      chineseDiseases,
      diseaseSearchKeyword,
      showDiseaseDialog,
      currentDiseaseType,
      openDiseaseDialog,
      selectDisease,
      removeDisease,
      searchDiseases
    }
  }
}
</script>

<template>
  <!-- 页面正文 -->
  <el-main style="width:100%;background:#fff;height:800px;overflow-y: auto">
    <el-form ref="form"  label-width="80px" label-position="left" size="mini" >
      <el-row style="background-color: #EAF1F5;margin-top: -20px">
        <el-col :span="24">
          <div style="display: flex; justify-content: center; gap: 8px;">
            <el-tooltip content="暂存当前填写内容" placement="top" effect="light">
              <el-button plain size="small" icon="el-icon-tickets" class="btn-save" @click="handleSave" style="padding: 4px 12px; min-width: 70px; border-radius: 12px; display: flex; justify-content: center; align-items: center; background-color: #90CAF9; color: #1565C0; border-color: #90CAF9;">
                <span style="display: flex; align-items: center; justify-content: center; margin-left: 4px;">暂存</span>
              </el-button>
            </el-tooltip>
            <el-tooltip content="提交当前病历信息" placement="top" effect="light">
              <el-button plain size="small" icon="el-icon-success" class="btn-submit" @click="handleSubmit" style="padding: 4px 12px; min-width: 70px; border-radius: 12px; display: flex; justify-content: center; align-items: center; background-color: #A5D6A7; color: #2E7D32; border-color: #A5D6A7;">
                <span style="display: flex; align-items: center; justify-content: center; margin-left: 4px;">提交</span>
              </el-button>
            </el-tooltip>
            <el-tooltip content="清空所有填写内容" placement="top" effect="light">
              <el-button plain size="small" icon="el-icon-printer" class="btn-clear" @click="handleClear" style="padding: 4px 12px; min-width: 70px; border-radius: 12px; display: flex; justify-content: center; align-items: center; background-color: #EF9A9A; color: #C62828; border-color: #EF9A9A;">
                <span style="display: flex; align-items: center; justify-content: center; margin-left: 4px;">清空所有</span>
              </el-button>
            </el-tooltip>
            <el-tooltip content="刷新当前页面数据" placement="top" effect="light">
              <el-button plain size="small" icon="el-icon-refresh" class="btn-refresh" @click="handleRefresh" style="padding: 4px 12px; min-width: 70px; border-radius: 12px; display: flex; justify-content: center; align-items: center; background-color: #FFCC80; color: #E65100; border-color: #FFCC80;">
                <span style="display: flex; align-items: center; justify-content: center; margin-left: 4px;">刷新</span>
              </el-button>
            </el-tooltip>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" align="left" >
          <el-tag>病史内容：</el-tag>
        </el-col>
      </el-row>

      <el-form-item label="主诉">
        <el-input type="textarea" :rows="1" v-model="medicalRecord.readme"
          :disabled="isSaved"></el-input>
      </el-form-item>
      <el-form-item label="现病史">
        <el-input type="textarea" :rows="2" v-model="medicalRecord.present"
          :disabled="isSaved"></el-input>
      </el-form-item>
      <el-form-item label="现病治疗情况">
        <el-input type="textarea" :rows="2" v-model="medicalRecord.presentTreat"
          :disabled="isSaved"></el-input>
      </el-form-item>
      <el-form-item label="既往史">
        <el-input type="textarea" :rows="2" v-model="medicalRecord.history"
          :disabled="isSaved"></el-input>
      </el-form-item>
      <el-form-item label="过敏史">
        <el-input type="textarea" :rows="2" v-model="medicalRecord.allergy"
          :disabled="isSaved"></el-input>
      </el-form-item>
      <el-form-item label="体格检查">
        <el-input type="textarea" :rows="2" v-model="medicalRecord.physique"
          :disabled="isSaved"></el-input>
      </el-form-item>

      <!-- 初步诊断部分 -->
      <el-row>
        <el-col :span="24" align="left">
          <el-tag>初步诊断：</el-tag>
        </el-col>
      </el-row>

      <el-form-item label="诊断类型">
        <el-radio-group v-model="diagnosisType" :disabled="isSaved">
          <el-radio label="western">西医诊断</el-radio>
          <el-radio label="chinese">中医诊断</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 西医诊断 -->
      <el-form-item label="西医诊断" v-if="diagnosisType === 'western'">
        <div class="diagnosis-container">
          <div class="selected-diseases">
            <el-tag
              v-for="disease in selectedWesternDiseases"
              :key="disease.id"
              closable
              @close="removeDisease(disease, 'western')"
              :disable-transitions="false"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ disease.diseaseName }}
            </el-tag>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="openDiseaseDialog('western')"
            :disabled="isSaved"
            style="margin-top: 8px;"
          >
            选择西医疾病
          </el-button>
        </div>
      </el-form-item>

      <!-- 中医诊断 -->
      <el-form-item label="中医诊断" v-if="diagnosisType === 'chinese'">
        <div class="diagnosis-container">
          <div class="selected-diseases">
            <el-tag
              v-for="disease in selectedChineseDiseases"
              :key="disease.id"
              closable
              @close="removeDisease(disease, 'chinese')"
              :disable-transitions="false"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ disease.diseaseName }}
            </el-tag>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="openDiseaseDialog('chinese')"
            :disabled="isSaved"
            style="margin-top: 8px;"
          >
            选择中医疾病
          </el-button>
        </div>
      </el-form-item>

    </el-form>
  </el-main>

  <!-- 患者选择对话框 -->
  <el-dialog
    title="选择患者"
    v-model="showPatientDialog"
    width="70%"
  >
    <el-table
      :data="patientList"
      @row-click="selectPatient"
      style="width: 100%"
      highlight-current-row
    >
      <el-table-column
        prop="caseNumber"
        label="病历号"
        width="120"
      />
      <el-table-column
        prop="name"
        label="姓名"
        width="120"
      />
      <el-table-column
        prop="gender"
        label="性别"
        width="80"
      />
    </el-table>
  </el-dialog>

  <!-- 疾病选择对话框 -->
  <el-dialog
    :title="currentDiseaseType === 'western' ? '选择西医疾病' : '选择中医疾病'"
    v-model="showDiseaseDialog"
    width="80%"
  >
    <!-- 搜索框 -->
    <div style="margin-bottom: 20px;">
      <el-input
        v-model="diseaseSearchKeyword"
        placeholder="请输入疾病名称或编码搜索"
        style="width: 300px; margin-right: 10px;"
        @keyup.enter="searchDiseases"
      />
      <el-button type="primary" @click="searchDiseases">搜索</el-button>
    </div>

    <!-- 疾病列表 -->
    <el-table
      :data="currentDiseaseType === 'western' ? westernDiseases : chineseDiseases"
      @row-click="selectDisease"
      style="width: 100%"
      highlight-current-row
      max-height="400"
    >
      <el-table-column
        prop="diseaseCode"
        label="疾病编码"
        width="120"
      />
      <el-table-column
        prop="diseaseName"
        label="疾病名称"
        width="200"
      />
      <el-table-column
        prop="diseaseICD"
        label="ICD编码"
        width="120"
      />
      <el-table-column
        prop="diseCategoryName"
        label="疾病分类"
        width="150"
      />
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showDiseaseDialog = false">取消</el-button>
      </span>
    </template>
  </el-dialog>

</template>

<style scoped>
.el-main {
  width: 100%;
  background: #fff;
  height: 800px;
  overflow-y: auto;
  padding: 20px;
}

.form-header {
  background-color: #EAF1F5;
  margin: -20px -20px 20px -20px;
  padding: 10px 20px;
}

.form-header .el-col {
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-button {
  padding: 8px 15px;
  font-size: 14px;
  border-radius: 4px;
  margin: 0 5px;
}

.form-button i {
  margin-right: 5px;
}

.btn-save {
  color: #409EFF;
  border-color: #409EFF;
}

.btn-submit {
  color: #67C23A;
  border-color: #67C23A;
}

.btn-clear {
  color: #F56C6C;
  border-color: #F56C6C;
}

.btn-refresh {
  color: #E6A23C;
  border-color: #E6A23C;
}

.el-tag {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-textarea {
  font-size: 14px;
}

/* 诊断相关样式 */
.diagnosis-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.selected-diseases {
  min-height: 40px;
  margin-bottom: 8px;
}

.selected-diseases .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-main {
    height: auto;
    min-height: 100vh;
    padding: 15px;
  }

  .form-header .el-col {
    margin-bottom: 10px;
  }

  .form-button {
    width: 100%;
    margin: 5px 0;
  }
}
</style>