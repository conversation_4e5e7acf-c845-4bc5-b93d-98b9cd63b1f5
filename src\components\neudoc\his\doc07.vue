<template>
	<!-- 8.患者账单 费用-->
	<div class="container">
		<h3>患者费用查询</h3>



		<!-- 患者选择 -->
		<div style="background-color: #EAF1F5; padding: 10px; margin: 10px 0;">
			<div style="margin-bottom: 10px;">
				<label>挂号ID：</label>
				<input
					v-model="inputRegistId"
					type="number"
					placeholder="请输入挂号ID"
					style="margin-right: 10px; padding: 5px;"
				>
				<button @click="loadDataByRegistId" style="margin-right: 10px;">查询费用</button>
				<button @click="refreshBill">刷新</button>
			</div>

			<div>
				<span>患者账单明细：</span>
				<span v-if="patientInfo.name">
					{{ patientInfo.name }} ({{ patientInfo.gender }}, {{ patientInfo.age }}岁)
				</span>
				<span v-else>
					{{ inputRegistId ? '请点击查询费用' : '请输入挂号ID查询患者费用' }}
				</span>
			</div>
		</div>

		<!-- 简化的表格 -->
		<table border="1" style="width: 100%; border-collapse: collapse;">
			<thead>
				<tr style="background-color: #f5f7fa;">
					<th>序号</th>
					<th>名称</th>
					<th>规格</th>
					<th>数量</th>
					<th>付数</th>
					<th>单价</th>
					<th>金额</th>
					<th>状态</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(item, index) in tableData01" :key="index">
					<td>{{ index + 1 }}</td>
					<td>
						{{ item.mingcheng }}
						<span v-if="item.type" :style="getTypeStyle(item.type)">
							[{{ getTypeText(item.type) }}]
						</span>
					</td>
					<td>{{ item.guige }}</td>
					<td>{{ item.shuliang }}</td>
					<td>{{ item.fushu }}</td>
					<td>{{ item.danjia }}元</td>
					<td style="font-weight: bold; color: #E6A23C;">{{ item.jiner }}元</td>
					<td>
						<span :style="getStatusStyle(item.zhuangtai)">
							{{ item.zhuangtai }}
						</span>
					</td>
				</tr>
				<tr v-if="tableData01.length === 0">
					<td colspan="8" style="text-align: center; padding: 20px;">暂无数据</td>
				</tr>
			</tbody>
		</table>

		<!-- 金额合计 -->
		<div style="margin: 10px 0; padding: 10px; background-color: #F5F7FA;">
			<span>金额合计：{{ totalAmount.toFixed(2) }}元</span>
			<span style="float: right;">共 {{ tableData01.length }} 项费用</span>
		</div>

	</div>
</template>

<script>
import { getReq } from '@/utils/api'
import { billStore } from '@/store/billStore'

export default {
	props: {
		// 患者ID或挂号ID
		patientId: {
			type: [Number, String],
			default: null
		},
		registId: {
			type: [Number, String],
			default: null
		}
	},

	data() {
		return {
			loading: false,
			inputRegistId: '', // 用户输入的挂号ID
			billStore // 引用全局store
		}
	},

	computed: {
		// 从store获取费用数据
		tableData01() {
			return this.billStore.billItems;
		},

		// 从store获取患者信息
		patientInfo() {
			return this.billStore.currentPatient;
		},

		// 从store获取总金额
		totalAmount() {
			return this.billStore.totalAmount;
		}
	},

	mounted() {
		console.log('doc07组件挂载，props:', { patientId: this.patientId, registId: this.registId });

		// 监听全局费用更新事件
		window.addEventListener('billUpdated', this.handleBillUpdate);

		// 如果store中没有数据，初始化默认数据
		if (this.billStore.billItems.length === 0) {
			if (this.patientId || this.registId) {
				this.loadBillData();
			} else {
				this.initDefaultData();
			}
		}
	},

	beforeUnmount() {
		// 移除事件监听
		window.removeEventListener('billUpdated', this.handleBillUpdate);
	},

	watch: {
		// 监听props变化，重新加载数据
		patientId(newVal) {
			if (newVal) {
				this.loadBillData();
			}
		},
		registId(newVal) {
			if (newVal) {
				this.loadBillData();
			}
		}
	},

	methods: {
		// 处理全局费用更新事件
		handleBillUpdate(event) {
			console.log('费用更新事件:', event.detail);
			// 由于使用了响应式store，数据会自动更新
		},

		// 初始化默认数据到store
		initDefaultData() {
			// 设置默认患者信息
			this.billStore.setCurrentPatient({
				name: '张三',
				gender: '男',
				age: '35',
				registId: 'R001'
			});

			// 添加默认费用项目
			this.billStore.addBillItem({
				type: 'registration',
				sourceId: 'R001',
				mingcheng: '挂号费',
				guige: '',
				shuliang: '1',
				fushu: '1',
				danjia: '5.00',
				jiner: '5.00',
				zhuangtai: '已收费'
			});

			this.billStore.addBillItem({
				type: 'check',
				sourceId: 'C001',
				mingcheng: '普通透视',
				guige: '',
				shuliang: '1',
				fushu: '1',
				danjia: '200.00',
				jiner: '200.00',
				zhuangtai: '已申请'
			});
				
			this.billStore.addBillItem({
				type: 'prescription',
				sourceId: 'P001',
				mingcheng: '小儿感冒颗粒',
				guige: '50mg*100片',
				shuliang: '10',
				fushu: '2',
				danjia: '5.00',
				jiner: '150.00',
				zhuangtai: '已发送'
			});

			this.billStore.addBillItem({
				type: 'prescription',
				sourceId: 'P002',
				mingcheng: '板蓝根颗粒',
				guige: '50mg*100片',
				shuliang: '5',
				fushu: '2',
				danjia: '5.00',
				jiner: '60.00',
				zhuangtai: '已发送'
			});
		},

			
		// 加载账单数据
		async loadBillData() {
			console.log('开始加载账单数据，patientId:', this.patientId, 'registId:', this.registId);

			if (!this.patientId && !this.registId) {
				console.log('没有患者ID或挂号ID，使用默认数据');
				this.setDefaultData();
				return;
			}

			this.loading = true;

			try {
				console.log('加载患者账单数据，registId:', this.registId, 'patientId:', this.patientId);

				// 并行加载多个数据源
				const promises = [];

				// 1. 加载挂号费用
				if (this.registId) {
					promises.push(this.loadRegistrationFee());
				}

				// 2. 加载检查申请费用
				promises.push(this.loadCheckApplyFees());

				// 3. 加载处方费用
				promises.push(this.loadPrescriptionFees());

				// 等待所有数据加载完成
				const results = await Promise.allSettled(promises);

				// 合并所有费用数据
				this.mergeBillData(results);

			} catch (error) {
				console.error('加载账单数据失败:', error);
				this.$message.error('请求失败: ' + error.message);
				this.setDefaultData();
			} finally {
				this.loading = false;
			}
		},

		// 加载挂号费用
		async loadRegistrationFee() {
			if (!this.registId) return [];

			try {
				const response = await getReq(`/regist/${this.registId}`);
				if (response.status === 200 && response.data) {
					const registData = response.data.data || response.data;

					// 设置患者信息
					if (registData.realName) {
						this.patientInfo = {
							name: registData.realName,
							gender: registData.gender,
							age: registData.age,
							registId: this.registId
						};
					}

					// 返回挂号费用项目
					return [{
						type: 'registration',
						mingcheng: '挂号费',
						guige: registData.deptName || '',
						shuliang: '1',
						fushu: '1',
						danjia: registData.registFee || '5.00',
						jiner: registData.registFee || '5.00',
						zhuangtai: '已收费',
						_originalData: registData
					}];
				}
			} catch (error) {
				console.error('加载挂号费用失败:', error);
			}
			return [];
		},

		// 加载检查申请费用
		async loadCheckApplyFees() {
			if (!this.registId) return [];

			try {
				const response = await getReq(`/his/apply/listCheck?registId=${this.registId}`);
				if (response.status === 200 && response.data) {
					const checkData = response.data.data || response.data;
					const checkList = Array.isArray(checkData) ? checkData : [];

					console.log('检查申请数据:', checkList);

					return checkList.map(item => ({
						type: 'check',
						mingcheng: item.name || item.itemName || '检查项目',
						guige: item.specification || item.spec || '',
						shuliang: '1',
						fushu: '1',
						danjia: item.price || item.fee || '0.00',
						jiner: item.price || item.fee || '0.00',
						zhuangtai: this.getCheckStatus(item.state || item.status),
						_originalData: item
					}));
				}
			} catch (error) {
				console.error('加载检查费用失败:', error);
			}
			return [];
		},

		// 加载处方费用
		async loadPrescriptionFees() {
			if (!this.registId) return [];

			try {
				// 获取处方列表
				const prescriptionResponse = await getReq(`/prescription/list?registId=${this.registId}`);
				if (prescriptionResponse.status !== 200) return [];

				const prescriptions = prescriptionResponse.data.data || prescriptionResponse.data || [];
				console.log('处方列表:', prescriptions);

				const prescriptionFees = [];

				// 遍历每个处方，获取药品明细
				for (const prescription of prescriptions) {
					try {
						const detailResponse = await getReq(`/prescription/detail/${prescription.id}`);
						if (detailResponse.status === 200) {
							const prescriptionDetail = detailResponse.data.data || detailResponse.data;

							// 处理处方明细
							if (prescriptionDetail && prescriptionDetail.prescriptionDetails) {
								const drugDetails = prescriptionDetail.prescriptionDetails;

								drugDetails.forEach(drug => {
									// 根据药品ID获取药品信息
									const drugInfo = this.getDrugInfoById(drug.drugsId);

									prescriptionFees.push({
										type: 'prescription',
										mingcheng: drugInfo ? drugInfo.name : `药品ID:${drug.drugsId}`,
										guige: drugInfo ? drugInfo.spec : '',
										shuliang: drug.amount || '1',
										fushu: drug.amount || '1',
										danjia: drugInfo ? drugInfo.price.toFixed(2) : '0.00',
										jiner: drugInfo ? (drugInfo.price * (drug.amount || 1)).toFixed(2) : '0.00',
										zhuangtai: this.getPrescriptionStatus(prescription.prescriptionState),
										_originalData: { prescription, drug, drugInfo }
									});
								});
							}
						}
					} catch (error) {
						console.error(`获取处方${prescription.id}明细失败:`, error);
					}
				}

				return prescriptionFees;
			} catch (error) {
				console.error('加载处方费用失败:', error);
			}
			return [];
		},

		// 合并账单数据
		mergeBillData(results) {
			const allBillItems = [];

			results.forEach((result, index) => {
				if (result.status === 'fulfilled' && Array.isArray(result.value)) {
					allBillItems.push(...result.value);
				} else if (result.status === 'rejected') {
					console.error(`数据源${index}加载失败:`, result.reason);
				}
			});

			this.tableData01 = allBillItems;
			console.log('合并后的账单数据:', this.tableData01);

			if (allBillItems.length === 0) {
				this.$message.info('暂无费用数据');
			} else {
				this.$message.success(`成功加载 ${allBillItems.length} 项费用`);
			}
		},

		// 根据药品ID获取药品信息
		getDrugInfoById(drugId) {
			const drugMap = {
				1: { name: '阿莫西林胶囊', spec: '0.25g*24粒', price: 12.50 },
				2: { name: '布洛芬缓释胶囊', spec: '0.3g*20粒', price: 18.00 },
				3: { name: '维生素C片', spec: '0.1g*100片', price: 8.50 },
				4: { name: '感冒灵颗粒', spec: '10g*12袋', price: 15.80 },
				5: { name: '头孢克肟胶囊', spec: '0.1g*12粒', price: 25.60 },
				9: { name: '复方甘草片', spec: '24片', price: 8.00 },
				10: { name: '小儿感冒颗粒', spec: '10g*12袋', price: 15.00 },
				29: { name: '板蓝根颗粒', spec: '10g*20袋', price: 12.00 },
				33: { name: '999感冒灵', spec: '10g*9袋', price: 18.50 },
				47: { name: '维C银翘片', spec: '12片*2板', price: 16.80 }
			};

			return drugMap[drugId] || null;
		},

		// 获取检查状态文本
		getCheckStatus(state) {
			const statusMap = {
				1: '已申请',
				2: '已确认',
				3: '已完成',
				4: '已取消'
			};
			return statusMap[state] || '未知状态';
		},

		// 获取处方状态文本
		getPrescriptionStatus(state) {
			const statusMap = {
				1: '暂存',
				2: '已发送',
				3: '已收费',
				4: '已作废'
			};
			return statusMap[state] || '未知状态';
		},

		// 处理账单数据
		processBillData(data) {
			let billData = [];
			let patientInfo = {};

			// 处理不同的数据结构
			if (data && data.data) {
				if (Array.isArray(data.data)) {
					billData = data.data;
				} else if (data.data.records) {
					billData = data.data.records;
					patientInfo = data.data.patientInfo || {};
				} else if (data.data.bills) {
					billData = data.data.bills;
					patientInfo = data.data.patient || {};
				}
			} else if (Array.isArray(data)) {
				billData = data;
			}

			// 转换数据格式
			this.tableData01 = billData.map(item => ({
				mingcheng: item.itemName || item.name || item.mingcheng || '未知项目',
				guige: item.specification || item.spec || item.guige || '',
				shuliang: item.quantity || item.shuliang || '1',
				fushu: item.dosage || item.fushu || '1',
				danjia: item.price || item.danjia || '0.00',
				jiner: item.amount || item.jiner || '0.00',
				zhuangtai: this.getStatusText(item.status || item.state || item.zhuangtai),
				// 保留原始数据
				_originalData: item
			}));

			// 设置患者信息
			if (patientInfo.name) {
				this.patientInfo = {
					name: patientInfo.name || patientInfo.patientName || '',
					gender: patientInfo.gender || patientInfo.sex || '',
					age: patientInfo.age || '',
					registId: patientInfo.registId || this.registId
				};
			}

			console.log('处理后的账单数据:', this.tableData01);
			console.log('患者信息:', this.patientInfo);
		},

		// 设置默认数据（当API不可用时）
		setDefaultData() {
			this.tableData01 = [
				{
					mingcheng: '挂号费',
					guige: '',
					shuliang: '1',
					fushu: '1',
					danjia: '5.00',
					jiner: '5.00',
					zhuangtai: '已收费',
				},
				{
					mingcheng: 'B超常规检查',
					guige: '',
					shuliang: '1',
					fushu: '1',
					danjia: '200.00',
					jiner: '200.00',
					zhuangtai: '划价',
				},
				{
					mingcheng: '呋喃妥因肠溶片',
					guige: '50mg*100片',
					shuliang: '2',
					fushu: '2',
					danjia: '5.00',
					jiner: '10.00',
					zhuangtai: '划价',
				}
			];

			this.patientInfo = {
				name: '张三',
				gender: '男',
				age: '35',
				registId: 'R001'
			};
		},

		// 根据输入的挂号ID加载数据
		loadDataByRegistId() {
			if (!this.inputRegistId) {
				alert('请输入挂号ID');
				return;
			}

			// 临时设置registId，触发数据加载
			this.registId = parseInt(this.inputRegistId);
			console.log('设置registId为:', this.registId);
			this.loadBillData();
		},

		// 刷新账单数据
		refreshBill() {
			this.loadBillData();
		},

		// 格式化价格显示
		formatPrice(price) {
			const num = parseFloat(price) || 0;
			return `${num.toFixed(2)}元`;
		},

		// 获取状态标签类型
		getStatusType(status) {
			const statusTypes = {
				// 收费状态
				'已收费': 'success',
				'划价': 'warning',
				'未收费': 'danger',
				'已退费': 'info',
				// 检查状态
				'已申请': 'primary',
				'已确认': 'warning',
				'已完成': 'success',
				'已取消': 'info',
				// 处方状态
				'暂存': 'info',
				'已发送': 'primary',
				'已作废': 'danger'
			};
			return statusTypes[status] || 'info';
		},

		// 获取状态文本
		getStatusText(status) {
			const statusTexts = {
				1: '已收费',
				2: '划价',
				3: '未收费',
				4: '已退费'
			};
			return statusTexts[status] || status || '未知';
		},

		// 获取费用类型文本
		getTypeText(type) {
			const typeTexts = {
				'registration': '挂号',
				'check': '检查',
				'prescription': '药品'
			};
			return typeTexts[type] || type;
		},

		// 获取费用类型标签样式
		getTypeTagType(type) {
			const typeStyles = {
				'registration': '',
				'check': 'success',
				'prescription': 'warning',
				'lab': 'primary',
				'treatment': 'info'
			};
			return typeStyles[type] || '';
		},

		// 获取类型样式
		getTypeStyle(type) {
			const typeStyles = {
				'registration': 'color: #909399; font-size: 12px;',
				'check': 'color: #67C23A; font-size: 12px;',
				'prescription': 'color: #E6A23C; font-size: 12px;',
				'lab': 'color: #409EFF; font-size: 12px;',
				'treatment': 'color: #F56C6C; font-size: 12px;'
			};
			return typeStyles[type] || 'color: #909399; font-size: 12px;';
		},

		// 获取状态样式
		getStatusStyle(status) {
			const statusStyles = {
				'已收费': 'color: #67C23A; font-weight: bold;',
				'已申请': 'color: #409EFF;',
				'已发送': 'color: #E6A23C;',
				'已确认': 'color: #E6A23C;',
				'已完成': 'color: #67C23A;',
				'已作废': 'color: #F56C6C;',
				'已取消': 'color: #909399;'
			};
			return statusStyles[status] || 'color: #909399;';
		}
	}
}
</script>

<style scoped>
.container {
  padding: 10px;
}

.el-table {
  border-radius: 6px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.el-table td {
  padding: 8px 0;
}

/* 金额合计区域样式 */
.el-row:last-child {
  border-radius: 6px;
  border: 1px solid #EBEEF5;
}

/* 状态标签样式 */
.el-tag {
  border-radius: 12px;
}

/* 空状态样式 */
.el-empty {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 5px;
  }

  .el-table {
    font-size: 12px;
  }

  .el-table th,
  .el-table td {
    padding: 6px 4px;
  }
}
</style>
