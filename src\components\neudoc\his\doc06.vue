<template>
  <!-- 7.草药处方 -->
  <el-container>
    <!-- 页面弹窗 -->
    <el-dialog
      title="添加药品"
      v-model="dialog01Visible"
      width="50%"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="true"
      append-to-body
      :z-index="3000"
    >
      <el-form size="mini">
        <el-form-item label="药品选择">
          <el-select
            v-model="selectedDrugId"
            :key="selectKey"
            placeholder="请选择或搜索药品"
            filterable
            remote
            reserve-keyword
            :remote-method="handleRemoteSearch"
            :loading="loading"
            style="width: 100%;"
            size="small"
            @change="handleDrugSelectById"
            @visible-change="handleSelectVisible"
            clearable
          >
            <el-option
              v-for="drug in drugList"
              :key="drug.id"
              :label="drug.name"
              :value="drug.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                  <div style="font-weight: bold; color: #303133; margin-bottom: 2px;">{{ drug.name }}</div>
                  <div style="font-size: 11px; color: #909399;">
                    规格: {{ drug.spec }} | 单位: {{ drug.unit }} | 助记码: {{ drug.mnemonicCode || drug.code }}
                  </div>
                </div>
                <div style="color: #E6A23C; font-weight: bold; margin-left: 10px;">{{ drug.price }}元</div>
              </div>
            </el-option>
          </el-select>
          <div style="margin-top: 5px; font-size: 11px; color: #909399;">
            💡 可以输入药品名称、编码或助记码进行搜索
            <br>
            🔍 调试信息：当前药品数量 {{ drugList.length }}
          </div>

          <!-- 备用搜索框 -->
          <div style="margin-top: 10px;">
            <el-input
              v-model="drugSearchKey"
              placeholder="备用搜索：输入药品名称、编码或助记码"
              size="small"
              @keyup.enter.native="searchDrugs"
            >
              <el-button slot="append" icon="el-icon-search" @click="searchDrugs" :loading="loading"></el-button>
            </el-input>
          </div>

          <!-- 备用药品列表显示 -->
          <div v-if="drugList.length > 0" style="margin-top: 10px; max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px;">
            <div
              v-for="drug in drugList.slice(0, 10)"
              :key="drug.id"
              @click.stop="selectDrugFromList(drug)"
              style="padding: 8px 12px; border-bottom: 1px solid #f0f0f0; cursor: pointer;"
              :style="{ backgroundColor: selectedDrugId === drug.id ? '#e6f7ff' : 'white' }"
            >
              <div style="font-weight: bold; color: #303133;">{{ drug.name }}</div>
              <div style="font-size: 12px; color: #909399;">
                规格: {{ drug.spec }} | 单位: {{ drug.unit }} | 价格: {{ drug.price }}元
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="药品信息">
          <!-- 显示选中的药品信息 -->
          <div v-if="selectedDrug.id" style="margin-bottom: 10px; padding: 10px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
            <div style="font-weight: bold; color: #1890ff;">已选择：{{ selectedDrug.name }}</div>
            <div style="font-size: 12px; color: #666;">
              规格：{{ selectedDrug.spec }} | 单位：{{ selectedDrug.unit }} | 价格：{{ selectedDrug.price }}元
            </div>
          </div>
          <div v-else style="margin-bottom: 10px; padding: 10px; background-color: #fafafa; border: 1px solid #d9d9d9; border-radius: 4px; color: #999;">
            请先选择药品
          </div>

          <el-form :model="selectedDrug" label-width="80px" size="mini">
            <el-form-item label="用法">
              <!-- 使用原生select测试 -->
              <select v-model="selectedDrug.usage" style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;">
                <option value="">请选择用法</option>
                <option value="口服">口服</option>
                <option value="外用">外用</option>
                <option value="静脉注射">静脉注射</option>
                <option value="肌肉注射">肌肉注射</option>
              </select>
            </el-form-item>
            <el-form-item label="用量">
              <el-input v-model="selectedDrug.dosage" placeholder="请输入用量"></el-input>
            </el-form-item>
            <el-form-item label="频次">
              <!-- 使用原生select测试 -->
              <select v-model="selectedDrug.frequency" style="width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;">
                <option value="">请选择频次</option>
                <option value="每日一次">每日一次</option>
                <option value="每日两次">每日两次</option>
                <option value="每日三次">每日三次</option>
                <option value="每日四次">每日四次</option>
                <option value="隔日一次">隔日一次</option>
                <option value="每周一次">每周一次</option>
              </select>
            </el-form-item>
            <el-form-item label="数量">
              <el-input-number 
                v-model="selectedDrug.quantity" 
                :min="1" 
                :max="99"
                controls-position="right"
              ></el-input-number>
            </el-form-item>
          </el-form>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="addDrugToPrescription(selectedDrug)"
            :disabled="!selectedDrug.id"
          >
            添加
          </el-button>
          <el-button @click="dialog01Visible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="保存为常用模板" :visible.sync="dialog02Visible" width="30%">
      <el-form size="mini">
        <el-form-item>
          <el-input placeholder="请输入内容" class="input-with-select" style="width: 100%">
            <template slot="prepend">模板名称：</template>
          </el-input>
        </el-form-item>

        <el-form-item label="模板范围">
          <el-radio v-model="dialogRadio01" label="1">个人</el-radio>
          <el-radio v-model="dialogRadio01" label="2">科室</el-radio>
          <el-radio v-model="dialogRadio01" label="3">全院</el-radio>
        </el-form-item>
        <el-form-item label="已选择药品">
          <el-table border :data="tableData03" size="mini">
            <el-table-column prop="choose02" label="" width="30px">
              <template slot-scope="scope">
                <input type="radio" name="chooseOne01" />
              </template>
            </el-table-column>
            <el-table-column property="mingcheng" label="名称">
            </el-table-column>
            <el-table-column property="guige" label="规格">
            </el-table-column>
            <el-table-column property="danwei" label="单位" width="60">
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="dialog02Visible = false">确 定</el-button>
          <el-button @click="dialog02Visible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="增加处方" v-model="dialog03Visible" width="30%">
      <el-form size="mini">
        <el-form-item>
          <el-input 
            v-model="tempPrescriptionName"
            placeholder="不输入将使用默认名称" 
            class="input-with-select" 
            style="width: 100%"
            @keyup.enter.native="confirmAddPrescription"
          >
            <template slot="prepend">处方名称：</template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="confirmAddPrescription">确 定</el-button>
          <el-button @click="dialog03Visible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog title="选择处方模板" :visible.sync="dialog04Visible" width="50%">
      <el-table :data="templateList" border style="width: 100%">
        <el-table-column prop="name" label="模板名称" width="200"></el-table-column>
        <el-table-column prop="scope" label="范围" width="80"></el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="useTemplate(scope.row)">使用</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 模板明细对话框 -->
    <el-dialog title="模板明细" :visible.sync="dialog05Visible" width="60%">
      <el-table :data="currentTemplateDetails" border style="width: 100%">
        <el-table-column prop="drugCode" label="药品编码" width="120"></el-table-column>
        <el-table-column prop="drugName" label="药品名称" width="180"></el-table-column>
        <el-table-column prop="spec" label="规格"></el-table-column>
        <el-table-column prop="unit" label="单位" width="80"></el-table-column>
        <el-table-column prop="usage" label="用法"></el-table-column>
        <el-table-column prop="dosage" label="用量"></el-table-column>
        <el-table-column prop="frequency" label="频次"></el-table-column>
      </el-table>
    </el-dialog>
    <!-- 页面正文 -->
    <el-header>
      <el-row>
        <el-tag>门诊诊断：</el-tag>
        <el-tag type="info">【中医诊断】风湿化热证</el-tag>
      </el-row>
      <el-row style="background-color: #EAF1F5">
        <el-col :span="7" style="margin-top: 4px;"><el-tag>门诊处方：</el-tag></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-circle-plus"
            @click="handleAddPrescription">增方</el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-remove"
            @click="deletePrescription">删方</el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-tickets"
            @click="savePrescription">保存</el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-success"
            @click="sendPrescription">发送</el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-delete"
            @click="cancelPrescription">作废</el-button></el-col>
        <el-col :span="3"><el-button type="text"></el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-circle-plus-outline"
            @click="openAddDrugDialog">增药</el-button></el-col>
        <el-col :span="2"><el-button type="text" class="el-icon-remove-outline"
            @click="deleteDrugFromPrescription">删药</el-button></el-col>
      </el-row>
    </el-header>
    <el-container>
      <el-aside width="250px">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>处方选择</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="saveAsTemplate">存为模板</el-button>
          </div>

          <!-- 处方下拉选择 -->
          <div class="prescription-selector">
            <el-select
              v-model="currentPrescriptionId"
              placeholder="请选择处方"
              style="width: 100%"
              @change="handlePrescriptionChange"
              :loading="loading"
              clearable
              filterable
            >
              <el-option
                v-for="prescription in tableData01"
                :key="prescription.id"
                :label="prescription.name"
                :value="prescription.id"
              >
                <span style="float: left">{{ prescription.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  <el-tag
                    :type="getPrescriptionStateType(prescription.state)"
                    size="mini"
                  >
                    {{ prescription.state }}
                  </el-tag>
                </span>
              </el-option>
            </el-select>

            <!-- 处方信息显示 -->
            <div v-if="currentPrescriptionInfo" class="prescription-info">
              <el-divider content-position="left">处方信息</el-divider>
              <div class="info-item">
                <span class="label">处方名称：</span>
                <span class="value">{{ currentPrescriptionInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态：</span>
                <el-tag
                  :type="getPrescriptionStateType(currentPrescriptionInfo.state)"
                  size="small"
                >
                  {{ currentPrescriptionInfo.state }}
                </el-tag>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDateTime(currentPrescriptionInfo.prescriptionTime) }}</span>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!tableData01 || tableData01.length === 0" class="empty-prescription">
              <el-empty description="暂无处方数据" />
              <el-button
                type="primary"
                size="small"
                @click="loadPrescriptions"
                :loading="loading"
              >
                重新加载
              </el-button>
            </div>
          </div>
        </el-card>
      </el-aside>
      <el-main>
        <el-row>
          <el-col :span="24">
            处方金额：{{ calculateTotalAmount() }}元
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            处方金额：{{ calculateTotalAmount() }}元
          </el-col>
        </el-row>
        <el-table ref="singleTable" :data="tableData08" style="width: 100%" :show-header="true" v-loading="loading" element-loading-text="加载药品数据中..." border size="small">
          <template #empty>
            <div class="empty-drugs">
              <el-empty description="暂无药品数据" />
              <p style="color: #909399; font-size: 12px;">请选择处方后查看药品信息，或点击"增药"添加药品</p>
            </div>
          </template>
          <el-table-column type="selection" width="50">
          </el-table-column>
          <el-table-column property="mingcheng" label="药品名称" min-width="150" show-overflow-tooltip>
          </el-table-column>
          <el-table-column property="spec" label="规格" width="120" show-overflow-tooltip>
          </el-table-column>
          <el-table-column property="danwei" label="单位" width="60" align="center">
          </el-table-column>
          <el-table-column property="usage" label="用法" width="80" align="center">
          </el-table-column>
          <el-table-column property="yongliang" label="用量" width="80" align="center">
          </el-table-column>
          <el-table-column property="frequency" label="频次" width="80" align="center">
          </el-table-column>
          <el-table-column label="数量" width="80" align="center">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.quantity"
                :min="1"
                :max="999"
                size="mini"
                @change="updateDrugQuantity(scope.row)"
                style="width: 70px;">
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column property="danjia" label="单价" width="80" align="right">
          </el-table-column>
          <el-table-column label="小计" width="80" align="right">
            <template #default="scope">
              <span>{{ calculateItemTotal(scope.row) }}元</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <el-button type="text" size="small" @click="removeDrug(scope.$index)" style="color: #f56c6c;">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </el-container>
    <el-footer style="height: 500px">
      <el-tabs type="card" v-model="activeName">
        <el-tab-pane label="处方模板" name="first">
          <el-container direction="horizontal">
            <el-aside width="400px">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span style="font-size: small;">处方模板列表</span>
                  <el-input
                    v-model="templateSearchKey"
                    placeholder="请输入模板名称搜索"
                    clearable
                    size="small"
                    style="width: 200px; margin-left: 10px;"
                    @keyup.enter.native="loadTemplates"
                  >
                    <template #append>
                      <el-button icon="el-icon-search" @click="loadTemplates" />
                    </template>
                  </el-input>
                </div>
                <el-table :data="tableData06" style="width: 100%" ref="singleTable" highlight-current-row
                  :show-header='false'>
                  <el-table-column prop="choose02" label="" width="30px">
                    <template slot-scope="scope">
                      <input type="radio" name="chooseOne01" />
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="名称">
                  </el-table-column>

                </el-table>
              </el-card>
            </el-aside>
            <el-main>
              <el-card class="box-card" style="margin-top: -20px;margin-left: -20px;">
                <div slot="header" class="clearfix">
                  <span style="font-size: small;">模板明细</span>
                  <el-button style="float: right; padding: 3px 0" type="text"
                    @click="useCurrentTemplate">使用该模板</el-button>
                </div>
                <el-table :data="currentTemplateDetails" style="width: 100%" ref="templateTable">
                  <el-table-column property="drugName" label="药品名称*">
                  </el-table-column>
                  <el-table-column property="dosage" label="用量*" width="100">
                  </el-table-column>
                  <el-table-column property="unit" label="单位" width="60">
                  </el-table-column>
                  <el-table-column property="usage" label="用法" width="120">
                  </el-table-column>
                  <el-table-column property="frequency" label="频次" width="80">
                  </el-table-column>
                </el-table>
              </el-card>
            </el-main>
          </el-container>
        </el-tab-pane>
        <el-tab-pane label="常用药品" name="second">
          <el-tag type="success" closable @close="showConfirm('删除该常用药品')"><span
              @click="showConfirm('在处方中添加该药品')">5%葡萄糖注射液
              250ml*1袋</span></el-tag>
          <el-tag type="success" closable @close="showConfirm('删除该常用药品')"><span
              @click="showConfirm('在处方中添加该药品')">5%葡萄糖注射液
              250ml*1袋</span></el-tag>
          <el-tag type="success" closable @close="showConfirm('删除该常用药品')"><span
              @click="showConfirm('在处方中添加该药品')">5%葡萄糖注射液
              250ml*1袋</span></el-tag>
        </el-tab-pane>
        <el-tab-pane label="建议方案" name="third">
          <el-tag type="warning">暂时不完成。。。</el-tag>
          <div style="font-size: small;font-family: '仿宋';">
            建议方案是事先根据一些常用的疾病的治疗方案，已经制定完成的处方方案；<br>
            当医生开立诊断后选择，系统可以根据诊断结果列出该疾病的病因、注意事项、针对该疾病的用药方案及相应的方案的说明及注意事项；<br>
            主要目的是用来辅助医生诊疗。
          </div>
        </el-tab-pane>
        <el-tab-pane label="历史处方" name="fourth">
          <el-container direction="horizontal">
            <el-aside width="400px">

              <el-table :data="tableData07" style="width: 100%" ref="singleTable" highlight-current-row
                :show-header='false'>
                <el-table-column prop="choose02" label="" width="30px">
                  <template slot-scope="scope">
                    <input type="radio" name="chooseOne01" />
                  </template>
                </el-table-column>
                <el-table-column width="160px" prop="datetime" label="处方时间">
                </el-table-column>
                <el-table-column prop="name01" label="诊断结果">
                </el-table-column>
                <el-table-column prop="name02" label="处方名称">
                </el-table-column>
              </el-table>

            </el-aside>
            <el-main>
              <el-card class="box-card" style="margin-top: -20px;margin-left: -20px;">
                <div slot="header" class="clearfix">
                  <span style="font-size: small;">处方明细</span>
                  <el-button style="float: right; padding: 3px 0; margin-left: 10px;" type="text"
                    @click="showConfirm('使用该历史处方')">使用该处方</el-button>
                  <el-button style="float: right; padding: 3px 0" type="text"
                    @click="refreshPrescriptionDrugs" :loading="loading">
                    <i class="el-icon-refresh"></i> 刷新
                  </el-button>
                </div>
                <el-table :data="tableData08" style="width: 100%" ref="singleTable">
                  <el-table-column property="mingcheng" label="药品名称*">
                  </el-table-column>
                  <el-table-column property="yongliang" label="用量*" width="100">
                  </el-table-column>
                  <el-table-column property="danwei" label="单位" width="60">
                  </el-table-column>
                  <el-table-column property="jiaozhu" label="脚注" width="120">
                  </el-table-column>
                  <el-table-column property="danjia" label="单价" width="80">
                  </el-table-column>
                </el-table>
              </el-card>
            </el-main>
          </el-container>
        </el-tab-pane>
      </el-tabs>
    </el-footer>
  </el-container>
</template>

<style scoped>
.prescription-selector {
  padding: 16px;
}

.prescription-info {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item .value {
  color: #303133;
  flex: 1;
}

.empty-prescription {
  text-align: center;
  padding: 40px 20px;
}

/* 下拉选项样式 */
.el-select-dropdown__item {
  height: auto;
  line-height: 1.5;
  padding: 8px 20px;
}

/* 处方信息分割线 */
.el-divider {
  margin: 12px 0;
}

.el-divider__text {
  font-size: 12px;
  color: #909399;
}
</style>

<script>
import {
  showMsgTitle,
  showMsgConfirm,
  postReq as postRequest,
  deleteRequest,
  putRequest,
  getReq as getRequest
} from '../../../utils/api'
import { addPrescriptionBill } from '../../../utils/billUtils'
export default {
  computed: {
    // 当前选中的处方信息
    currentPrescriptionInfo() {
      if (!this.currentPrescriptionId || !this.tableData01) {
        return null;
      }
      return this.tableData01.find(prescription => prescription.id === this.currentPrescriptionId);
    }
  },

  methods: {
    // 辅助函数：根据处方状态返回类型名称
    getPrescriptionType(state) {
      const types = {
        1: '草药',
        2: '西药',
        3: '检查',
        4: '检验',
        5: '处置'
      };
      return types[state] || '医疗';
    },

    // 辅助函数：根据处方状态返回状态文本
    getPrescriptionStateText(state) {
      const stateTexts = {
        1: '暂存',
        2: '已发送',
        3: '已收费',
        4: '已作废'
      };
      return stateTexts[state] || '未知状态';
    },

    // 辅助函数：根据处方状态返回标签类型
    getPrescriptionStateType(state) {
      const stateTypes = {
        '暂存': 'warning',
        '已发送': 'primary',
        '已收费': 'success',
        '已作废': 'danger'
      };
      return stateTypes[state] || 'info';
    },

    // 处方选择变化事件
    handlePrescriptionChange(prescriptionId) {
      console.log('选择处方ID:', prescriptionId);
      this.currentPrescriptionId = prescriptionId;

      if (prescriptionId) {
        // 加载选中处方的药品数据
        this.loadPrescriptionDrugs(prescriptionId);
      } else {
        // 清空药品数据
        this.tableData08 = [];
      }
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return '--';
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '--';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '--';
      }
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    },
    
    showConfirm(msg) {
      showMsgConfirm(this, msg);
    },
    showMsg(msg) {
      showMsgTitle(this, msg);
    },
    handleCurrentChange() {

    },
    handleAddPrescription() {
      this.dialog03Visible = true;
      this.tempPrescriptionName = '';
    },

    // 打开模板选择对话框
    openTemplateDialog() {
      this.dialog04Visible = true;
      this.loadTemplates();
    },

    // 加载处方模板
    loadTemplates() {
      this.loading = true;
      getRequest('/prescription/templates').then(resp => {
        if (resp.status === 200) {
          this.templateList = resp.data;
        } else {
          this.$message.error('加载模板失败');
        }
      }).catch(error => {
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },

    // 使用模板创建处方
    useTemplate(template) {
      this.loading = true;
      postRequest('/prescription/create-from-template', {
        templateId: template.id,
        patientId: this.currentPatientId
      }).then(resp => {
        if (resp.status === 200) {
          this.$message.success('处方创建成功');
          this.loadPrescriptions();
          this.dialog04Visible = false;
        } else {
          this.$message.error('处方创建失败');
        }
      }).catch(error => {
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },

    // 检查处方药品数量是否已达上限
    checkDrugLimit() {
      return this.tableData08.length < 5;
    },

    // 打开增药对话框
    openAddDrugDialog() {
      console.log('点击增药按钮');
      console.log('当前选中处方ID:', this.currentPrescriptionId);
      console.log('处方列表:', this.tableData01);

      // 检查是否选中了处方
      if (!this.currentPrescriptionId) {
        this.$message.warning('请先选择一个处方');
        return;
      }

      // 获取当前选中处方的状态
      const currentPrescription = this.tableData01.find(
        item => item.id === this.currentPrescriptionId
      );

      console.log('当前选中处方:', currentPrescription);

      if (!currentPrescription) {
        this.$message.warning('未找到选中的处方，请重新选择');
        return;
      }

      // 检查处方状态，只有暂存状态可增药
      if (currentPrescription.state !== '暂存') {
        this.$message.warning(`当前处方状态为"${currentPrescription.state}"，只有暂存状态的处方可以增药`);
        return;
      }

      // 检查药品数量限制
      if (!this.checkDrugLimit()) {
        this.$message.warning('每个处方最多只能包含5种药品，请新增处方');
        return;
      }

      // 清空之前的搜索结果和选中状态
      this.drugSearchKey = '';
      this.selectedDrug = {
        id: null,
        code: '',
        name: '',
        spec: '',
        unit: '',
        usage: '',
        dosage: '',
        frequency: '',
        quantity: 1
      };

      // 初始化时加载一些常用药品，而不是等待用户搜索
      this.loadInitialDrugs();

      // 清空之前的选择
      this.selectedDrugId = null;

      // 如果没有数据，重置加载标志
      if (this.drugList.length === 0) {
        this.drugsLoaded = false;
      }

      // 打开药品选择对话框
      this.dialog01Visible = true;
    },

    // 添加药品到处方
    addDrugToPrescription(drug) {
      if (!this.currentPrescriptionId) {
        this.$message.warning('请先选择一个处方');
        return;
      }

      if (!drug.id) {
        this.$message.warning('请先选择要添加的药品');
        return;
      }

      // 验证必填字段
      if (!this.selectedDrug.dosage || !this.selectedDrug.frequency) {
        this.$message.warning('请填写用量和频次');
        return;
      }

      console.log('添加药品到处方:', drug);
      console.log('当前处方ID:', this.currentPrescriptionId);

      this.loading = true;

      // 构建处方明细数据 - 匹配实际数据库表结构 prescriptiondetailed
      const prescriptionDetail = {
        prescriptionId: parseInt(this.currentPrescriptionId), // PrescriptionID
        drugsId: parseInt(this.selectedDrug.id), // DrugsID (注意是DrugsID不是drugId)
        drugsUsage: this.selectedDrug.usage || '口服', // DrugsUsage
        dosage: this.selectedDrug.dosage, // Dosage
        frequency: this.selectedDrug.frequency, // Frequency
        amount: parseFloat((this.selectedDrug.price * this.selectedDrug.quantity).toFixed(2)), // Amount
        state: 1 // State
      };

      console.log('=== 发送到后端的数据 ===');
      console.log('prescriptionDetail:', JSON.stringify(prescriptionDetail, null, 2));
      console.log('selectedDrug完整数据:', JSON.stringify(this.selectedDrug, null, 2));
      console.log('当前选中药品:', this.selectedDrug);
      console.log('请求URL: /prescription/detail/add');
      console.log('请求方法: POST');
      console.log('=== 字段检查 ===');
      console.log('drugsId:', prescriptionDetail.drugsId, typeof prescriptionDetail.drugsId);
      console.log('drugsUsage:', prescriptionDetail.drugsUsage, typeof prescriptionDetail.drugsUsage);
      console.log('amount:', prescriptionDetail.amount, typeof prescriptionDetail.amount);
      console.log('dosage:', prescriptionDetail.dosage, typeof prescriptionDetail.dosage);

      postRequest('/prescription/detail/add', prescriptionDetail).then(resp => {
        console.log('=== 后端响应数据 ===');
        console.log('响应状态:', resp.status);
        console.log('响应数据:', JSON.stringify(resp.data, null, 2));

        if (resp.status === 200) {
          this.$message.success('药品添加成功');

          // 立即添加到本地列表，避免被重新加载覆盖
          const newDrugItem = {
            id: Date.now(), // 临时ID
            mingcheng: this.selectedDrug.name,
            yongliang: this.selectedDrug.dosage,
            danwei: this.selectedDrug.unit,
            jiaozhu: '',
            danjia: `${this.selectedDrug.price}元`,
            quantity: this.selectedDrug.quantity,
            usage: this.selectedDrug.usage,
            frequency: this.selectedDrug.frequency,
            spec: this.selectedDrug.spec,
            price: this.selectedDrug.price,
            _isNewlyAdded: true // 标记为新添加的数据
          };

          // 添加到列表
          this.tableData08.push(newDrugItem);

          // 重新计算总金额
          this.$forceUpdate(); // 强制更新界面以显示新的总金额

          // 不立即重新加载，避免覆盖新添加的数据
          // 如果需要同步最新数据，可以手动刷新
          console.log('药品添加成功，已更新本地列表');

          this.dialog01Visible = false;

          // 清空选中的药品信息
          this.selectedDrug = {
            id: null,
            code: '',
            name: '',
            spec: '',
            unit: '',
            usage: '',
            dosage: '',
            frequency: '',
            quantity: 1
          };
        } else {
          console.error('药品添加失败，响应状态:', resp.status);
          console.error('失败响应数据:', resp);
          this.$message.error('药品添加失败');
        }
      }).catch(error => {
        console.error('添加药品错误:', error);
        console.error('错误详情:', error.response);
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误数据:', error.response.data);
        }
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },
    
    confirmAddPrescription() {
      let prescriptionName = this.tempPrescriptionName.trim();
      if (!prescriptionName) {
        // 获取当前处方数量，生成默认名称
        const count = this.tableData01.filter(item => 
          item.name.startsWith('新增处方')
        ).length + 1;
        prescriptionName = `新增处方${count}`;
      }
      
      this.loading = true;

      // 构建处方数据，包含更多必要字段
      const prescriptionData = {
        prescriptionName: prescriptionName,
        prescriptionTime: new Date().toISOString(),
        prescriptionState: 1, // 1表示暂存状态
        userId: 1, // 当前用户ID，这里暂时写死
        medicalId: 1, // 病历ID，这里暂时写死
        registId: 1, // 挂号ID，这里暂时写死
        keyword: prescriptionName
      };

      console.log('添加处方数据:', prescriptionData);

      postRequest('/prescription/add', prescriptionData).then(resp => {
        console.log('添加处方响应:', resp);
        if (resp.status === 200) {
          this.$message.success('处方添加成功');
          this.dialog03Visible = false;

          // 清空输入框
          this.tempPrescriptionName = '';

          // 刷新处方列表
          this.loadPrescriptions().then(() => {
            console.log('处方列表刷新完成');

            // 如果返回了新处方的ID，自动选中新添加的处方
            if (resp.data && resp.data.data && resp.data.data.id) {
              const newPrescriptionId = resp.data.data.id;
              console.log('新处方ID:', newPrescriptionId);

              // 延迟一下确保列表已更新
              this.$nextTick(() => {
                this.currentPrescriptionId = newPrescriptionId;
                this.loadPrescriptionDrugs(newPrescriptionId);
              });
            }
          });
        } else {
          this.$message.error('处方添加失败: ' + (resp.msg || '未知错误'));
        }
      }).catch(error => {
        console.error('添加处方错误:', error);
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },

    deletePrescription() {
      // 获取当前选中处方的状态
      const currentPrescription = this.tableData01.find(
        item => item.id === this.currentPrescriptionId
      );
      
      // 检查处方状态，只有暂存状态可删除
      if (!currentPrescription || currentPrescription.state !== '暂存') {
        this.$message.warning('只有暂存状态的处方可以删除');
        return;
      }

      this.$confirm('确认删除该处方?', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        deleteRequest('/prescription/' + this.currentPrescriptionId).then(resp => {
          if (resp.status === 200) {
            this.$message.success('处方删除成功');
            this.loadPrescriptions();
          } else {
            this.$message.error('处方删除失败');
          }
        }).catch(error => {
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    sendPrescription() {
      // 获取当前选中处方的状态
      const currentPrescription = this.tableData01.find(
        item => item.id === this.currentPrescriptionId
      );
      
      // 检查处方状态，只有暂存状态可发送
      if (!currentPrescription || currentPrescription.state !== '暂存') {
        this.$message.warning('只有暂存状态的处方可以发送');
        return;
      }

      this.$confirm('确认发送该处方?发送后收费员将可以收费', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        putRequest(`/prescription/send/${this.currentPrescriptionId}`).then(resp => {
          if (resp.status === 200) {
            this.$message.success('处方发送成功');

            // 自动添加到费用清单
            this.addPrescriptionToFeeList(currentPrescription);

            this.loadPrescriptions();
          } else {
            this.$message.error('处方发送失败');
          }
        }).catch(error => {
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    // 将处方添加到费用清单
    addPrescriptionToFeeList(prescription) {
      try {
        console.log('添加处方到费用清单:', prescription);

        // 获取当前处方的药品明细
        const prescriptionDrugs = this.tableData02 || [];

        if (prescriptionDrugs.length === 0) {
          console.log('处方中没有药品，不添加费用');
          return;
        }

        // 构建药品列表
        const drugList = prescriptionDrugs.map(drug => ({
          drugsId: drug.id || drug.drugsId,
          drugName: drug.mingcheng || drug.name,
          specification: drug.guige || drug.spec,
          price: parseFloat(drug.danjia) || 0,
          amount: parseInt(drug.quantity) || 1
        }));

        // 获取患者信息
        const patientInfo = {
          registId: this.currentRegistId,
          name: this.patientName || '未知患者',
          gender: this.patientGender || '',
          age: this.patientAge || ''
        };

        // 调用费用工具添加处方费用
        addPrescriptionBill(prescription, drugList, patientInfo);

        console.log('处方费用添加成功');
        this.$message.success(`处方"${prescription.prescriptionName}"已添加到费用清单`);

      } catch (error) {
        console.error('添加处方费用失败:', error);
        this.$message.error('添加费用失败: ' + error.message);
      }
    },



    cancelPrescription() {
      // 获取当前选中处方的状态
      const currentPrescription = this.tableData01.find(
        item => item.id === this.currentPrescriptionId
      );
      
      // 检查处方状态，只有已发送状态可作废
      if (!currentPrescription || currentPrescription.state !== '已发送') {
        this.$message.warning('只有已发送状态的处方可以作废');
        return;
      }

      this.$confirm('确认作废该处方?作废后收费员将无法看到该处方', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        putRequest('/prescription/cancel/' + this.currentPrescriptionId).then(resp => {
          if (resp.status === 200) {
            this.$message.success('处方作废成功');
            this.loadPrescriptions();
          } else {
            this.$message.error('处方作废失败');
          }
        }).catch(error => {
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    savePrescription() {
      this.loading = true;
      putRequest('/prescription/save/' + this.currentPrescriptionId, {
        name: this.currentPrescriptionName,
        items: this.tableData08
      }).then(resp => {
        if (resp.status === 200) {
          this.$message.success('处方保存成功');
          this.loadPrescriptions();
        } else {
          this.$message.error('处方保存失败');
        }
      }).catch(error => {
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },

    loadPrescriptions() {
      this.loading = true;
      // 增加超时时间为10秒
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 10000);
      });

      let request = getRequest('/prescription/list');

      return Promise.race([
        request,
        timeoutPromise
      ]).then(resp => {
        if (resp.status === 200) {
          console.log('处方列表API返回数据:', resp.data);

          // 处理Result<List<Prescription>>格式的响应
          let prescriptionData = [];
          if (resp.data && resp.data.data && Array.isArray(resp.data.data)) {
            prescriptionData = resp.data.data;
            console.log('使用resp.data.data，处方数量:', prescriptionData.length);
          } else if (Array.isArray(resp.data)) {
            prescriptionData = resp.data;
            console.log('使用resp.data，处方数量:', prescriptionData.length);
          } else {
            console.warn('API返回的处方数据格式不正确:', resp.data);
          }

          // 转换数据格式以适配前端显示
          this.tableData01 = prescriptionData.map(item => ({
            id: item.id,
            name: item.prescriptionName || item.name || `处方_${item.id}`,
            state: this.getPrescriptionStateText(item.prescriptionState),
            prescriptionTime: item.prescriptionTime,
            userId: item.userId,
            medicalId: item.medicalId,
            registId: item.registId
          }));

          console.log('处理后的处方列表:', this.tableData01);

          if (this.tableData01.length > 0) {
            this.$message.success(`成功加载 ${this.tableData01.length} 条处方数据`);
          } else {
            this.$message.info('暂无处方数据');
          }
        } else {
          this.$message.error('加载处方列表失败');
        }
      }).catch(error => {
        // 忽略取消和超时错误
        if (error.message !== '请求超时' && error.message !== 'cancel') {
          this.$message.error('请求失败: ' + error.message);
        }
        // 设置默认数据以防页面空白
        this.tableData01 = [
          { name: '新增处方01', state: '已发送', id: 1 },
          { name: '新增处方02', state: '暂存', id: 2 }
        ];
      }).finally(() => {
        this.loading = false;
      });
    },

    saveAsTemplate() {
      this.$confirm('确认保存为模板?', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        postRequest('/prescription/template/add', {
          name: this.templateName,
          scope: this.dialogRadio01,
          prescriptionId: this.currentPrescriptionId
        }).then(resp => {
          if (resp.status === 200) {
            this.$message.success('模板保存成功');
            this.dialog02Visible = false;
            this.loadTemplates();
          } else {
            this.$message.error('模板保存失败');
          }
        }).catch(error => {
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    useCurrentTemplate() {
      // 检查是否选择了模板
      if (!this.currentTemplateId) {
        this.$message.warning('请先选择一个处方模板');
        return;
      }

      // 检查是否选择了处方
      if (!this.currentPrescriptionId) {
        this.$message.warning('请先选择一个处方');
        return;
      }

      this.$confirm('确认使用该模板?', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        postRequest('/prescription/template/use/' + this.currentTemplateId, {
          prescriptionId: this.currentPrescriptionId
        }).then(resp => {
          if (resp.status === 200) {
            this.$message.success('模板使用成功');
            this.loadPrescriptions();
          } else {
            this.$message.error('模板使用失败');
          }
        }).catch(error => {
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    loadTemplates() {
      this.loading = true;
      // 增加超时时间为10秒
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 10000);
      });

      // 构建查询参数
      const params = {
        userId: 1,    // 当前用户ID
        medicalId: 1, // 当前病历ID
        state: 1      // 只查询启用状态的模板
      };
      
      // 添加搜索关键词
      if (this.templateSearchKey) {
        params.keyword = this.templateSearchKey;
      }

      // 使用正确的处方模板API
      console.log('尝试加载处方模板，参数:', params);

      // 构建查询字符串 - 添加分页参数获取所有数据
      const queryParams = new URLSearchParams();
      if (params.userId) queryParams.append('userId', params.userId);
      if (params.medicalId) queryParams.append('medicalId', params.medicalId);
      if (params.state) queryParams.append('state', params.state);
      if (params.keyword) queryParams.append('keyword', params.keyword);

      // 添加分页参数，获取更多数据
      queryParams.append('pageSize', '100'); // 获取100条记录
      queryParams.append('pageNum', '1');

      // 由于后端模板API返回500错误，暂时使用处方数据作为模板
      const apiUrl = `/prescription/list?${queryParams.toString()}`;

      console.log('调用API (使用处方数据作为模板):', apiUrl);
      let request = getRequest(apiUrl);
      
      Promise.race([
        request,
        timeoutPromise
      ]).then(resp => {
        if (resp.status === 200) {
          console.log('API返回原始数据:', resp.data); // 详细调试日志

          // 处理API返回数据 - 后端返回Result<List<PrescriptionTemplate>>格式
          console.log('API返回完整数据结构:', JSON.stringify(resp.data, null, 2));

          let templateData = [];
          let dataList = [];

          // 检查不同的响应格式
          if (resp.data && resp.data.data && Array.isArray(resp.data.data)) {
            dataList = resp.data.data;
            console.log('使用resp.data.data，数据长度:', dataList.length);
          } else if (Array.isArray(resp.data)) {
            dataList = resp.data;
            console.log('使用resp.data，数据长度:', dataList.length);
          } else {
            console.warn('未识别的数据格式:', resp.data);
          }

          if (dataList && Array.isArray(dataList)) {
            console.log('开始处理数据列表，总数:', dataList.length);
            templateData = dataList.map((item, index) => {
              console.log(`处理前数据项[${index}]:`, JSON.stringify(item, null, 2)); // 打印完整数据项结构
              
              // 确保数据项是对象 - 更宽松的检查
              if (!item) {
                console.warn(`空数据项[${index}]:`, item);
                return null;
              }

              // 如果不是对象，尝试转换
              if (typeof item !== 'object') {
                console.warn(`非对象数据项[${index}]:`, item, '尝试转换');
                item = { id: index, name: String(item) };
              }
              
              // 获取处方名称 - 适配处方数据格式
              const getName = () => {
                // 优先使用prescriptionName字段（处方数据的主要名称字段）
                if (item.prescriptionName) {
                  return item.prescriptionName;
                }

                // 备选字段
                if (item.name) {
                  return item.name;
                }

                // 最后的备选逻辑
                return `处方_${item.id}`;
              };
              
              // 判断模板范围：userId为1是全院模板，其他是个人模板
              const getScope = () => {
                return item.userId === 1 ? '全院' : '个人';
              };
              
              const processedItem = {
                id: item.id || item.ID || item.templateId || item.templateID || index,
                name: getName(),
                scope: getScope(),
                // 保留其他字段供内部使用
                _prescriptionTime: item.prescriptionTime || item.PrescriptionTime || item.createTime || item.CreateTime || new Date().getTime(),
                _prescriptionState: item.prescriptionState || item.PrescriptionState || item.status || 1,
                _userId: item.userId
              };
              
              console.log(`处理后数据项[${index}]:`, processedItem);
              return processedItem;
            }).filter(item => {
              if (item === null) {
                console.warn('过滤掉null项');
                return false;
              }
              return true;
            }); // 过滤掉无效项

            console.log('过滤后的模板数据数量:', templateData.length);

            if (templateData.length === 0 && dataList.length > 0) {
              console.warn('所有数据项处理后均为空，请检查API返回数据结构');
              console.log('原始数据列表:', dataList);
            }
          } else {
            console.warn('API返回的数据不是数组:', resp.data);
          }
          
          console.log('最终模板数据:', templateData); // 更清晰的调试日志
          
          // 同时更新templateList和tableData06
          this.templateList = templateData;
          this.tableData06 = templateData;
          
          // 如果没有数据，显示提示信息
          if (templateData.length === 0) {
            this.$message.info('没有找到符合条件的处方模板');
          } else {
            this.$message.success(`成功加载 ${templateData.length} 条模板数据`);
          }
        } else {
          this.$message.error('加载模板列表失败');
        }
      }).catch(error => {
        // 忽略取消和超时错误
        if (error.message !== '请求超时' && error.message !== 'cancel') {
          this.$message.error('请求失败: ' + error.message);
        }
        // 设置默认模板数据以防页面空白
        const defaultTemplates = [
          { name: '病毒性感冒处方', id: 1 },
          { name: '寒湿瘀郁证处方', id: 2 },
          { name: '细菌炎症处方', id: 3 }
        ];
        this.templateList = defaultTemplates;
        this.tableData06 = defaultTemplates;
      }).finally(() => {
        this.loading = false;
      });
      
      // 返回请求对象以便取消
      return request;
    },

    handleCurrentChange(val) {
      if (val && val.id) {
        this.currentPrescriptionId = val.id;
        this.currentPrescriptionName = val.name;
        console.log('选中处方:', val.name, 'ID:', val.id);

        // 确保药品数据已加载，以便正确显示处方药品信息
        if (this.drugList.length === 0) {
          console.log('预加载药品数据以支持处方药品显示');
          this.loadInitialDrugs();
        }

        // 测试药品列表API（仅在开发环境）
        if (process.env.NODE_ENV === 'development') {
          this.testDrugListAPI();
        }

        // 加载选中处方的药品数据
        this.loadPrescriptionDrugs(val.id);
      } else {
        this.currentPrescriptionId = null;
        this.currentPrescriptionName = '';
        this.tableData08 = [];
      }
    },

    handleTemplateSelect(val) {
      this.currentTemplateId = val.id;
    },

    // 加载初始药品数据（打开对话框时）
    loadInitialDrugs() {
      // 如果已经加载过且有数据，不重复加载
      if (this.drugsLoaded && this.drugList.length > 0) {
        console.log('药品数据已加载，跳过重复加载');
        return;
      }

      console.log('加载初始药品数据');
      this.loading = true;

      // 使用独立的药品API
      const params = new URLSearchParams();
      params.append('pageSize', '20');

      console.log('加载药品数据:', `/api/drugs/list?${params.toString()}`);

      getRequest(`/api/drugs/list?${params.toString()}`).then(resp => {
        console.log('药品数据响应:', resp);
        this.processDrugResponse(resp, '加载初始药品数据');
      }).catch(error => {
        console.error('加载初始药品数据失败:', error);
        console.log('尝试使用Fmeditem接口作为备用方案');

        // 备用方案：使用Fmeditem的drugs接口
        getRequest('/fmeditem/drugs?count=20&pn=1').then(resp => {
          console.log('备用药品数据响应:', resp);
          this.processDrugResponse(resp, '加载初始药品数据');
        }).catch(err => {
          console.error('备用接口也失败:', err);
          this.setDefaultDrugData();
          this.$message.warning('药品接口不可用，显示测试数据。请检查DrugsController和数据库配置');
        });
      }).finally(() => {
        this.loading = false;
      });
    },

    // 远程搜索方法
    handleRemoteSearch(query) {
      console.log('远程搜索:', query);
      if (query !== '') {
        this.drugSearchKey = query;
        this.searchDrugs();
      } else {
        // 如果搜索为空，加载初始数据
        this.loadInitialDrugs();
      }
    },

    // 处理下拉框显示状态变化
    handleSelectVisible(visible) {
      console.log('下拉框显示状态变化:', visible);
      console.log('当前药品列表长度:', this.drugList.length);

      // 只在第一次打开且没有数据时加载
      if (visible && this.drugList.length === 0 && !this.loading) {
        console.log('下拉框打开且无数据，开始加载初始数据');
        this.loadInitialDrugs();
      }
    },

    // 搜索药品
    searchDrugs() {
      console.log('开始搜索药品，关键词:', this.drugSearchKey);

      this.loading = true;

      // 构建查询参数 - 使用独立的药品API
      const params = new URLSearchParams();
      params.append('keyword', this.drugSearchKey);
      params.append('pageSize', '50'); // 搜索时返回更多结果

      console.log('发送药品搜索请求:', `/api/drugs/list?${params.toString()}`);

      getRequest(`/api/drugs/list?${params.toString()}`).then(resp => {
        this.processDrugResponse(resp, '搜索药品');
      }).catch(error => {
        console.error('药品搜索请求失败:', error);
        console.error('错误详情:', error.response);

        // 如果是网络错误或后端不可用，提供测试数据
        if (error.message.includes('Network Error') || error.code === 'ECONNREFUSED') {
          console.log('后端服务不可用，使用测试数据');
          this.setDefaultDrugData();
          this.$message.warning('后端服务连接失败，显示测试数据。请检查后端服务是否启动。');
        } else {
          this.$message.error('请求失败: ' + error.message);
        }
      }).finally(() => {
        this.loading = false;
      });
    },

    // 通用药品响应处理方法
    processDrugResponse(resp, operation) {
      console.log(`${operation}响应:`, resp);
      console.log('响应状态:', resp.status);
      console.log('响应数据结构:', JSON.stringify(resp.data, null, 2));

      if (resp.status === 200) {
        console.log(`${operation}结果:`, resp.data);

        // 处理药品搜索结果 - 适配多种数据格式
        let drugData = [];

        // 检查直接返回数组格式（DrugsController返回的格式）
        if (Array.isArray(resp.data)) {
          drugData = resp.data;
          console.log('使用直接数组格式，药品数量:', drugData.length);
        }
        // 检查JsonResult格式: {result: true, data: [...]}
        else if (resp.data && resp.data.result && resp.data.data && Array.isArray(resp.data.data)) {
          drugData = resp.data.data;
          console.log('使用JsonResult.data格式，药品数量:', drugData.length);
        }
        // 检查嵌套data格式
        else if (resp.data && resp.data.data && Array.isArray(resp.data.data)) {
          drugData = resp.data.data;
          console.log('使用嵌套data格式，药品数量:', drugData.length);
        }
        else {
          console.warn('未识别的响应数据格式:', resp.data);
          console.log('响应数据类型:', typeof resp.data);
          console.log('响应数据内容:', resp.data);
          drugData = [];
        }

        // 转换药品数据格式 - 根据您的真实Drugs表字段映射
        this.drugList = drugData.map((item, index) => {
          console.log(`处理药品数据项[${index}]:`, item);

          // 根据您的真实数据库字段进行映射：
          // drugsName: 药品名称
          // drugsCode: 药品编码
          // drugsFormat: 药品规格
          // drugsUnit: 药品单位
          // drugsPrice: 药品价格
          // mnemonicCode: 助记码
          // manufacturer: 生产厂家
          const drugName = item.drugsName || item.itemName || item.name || '未知药品';
          const drugCode = item.drugsCode || item.itemCode || item.mnemonicCode || item.code || '';
          const drugSpec = item.drugsFormat || item.format || item.spec || '';
          const drugUnit = item.drugsUnit || item.unit || '个';
          const drugPrice = parseFloat(item.drugsPrice) || parseFloat(item.price) || 0;
          const drugMnemonic = item.mnemonicCode || item.code || '';
          const drugManufacturer = item.manufacturer || '';

          console.log(`药品信息映射: 名称=${drugName}, 编码=${drugCode}, 规格=${drugSpec}, 单位=${drugUnit}, 价格=${drugPrice}, 助记码=${drugMnemonic}, 厂家=${drugManufacturer}`);

          return {
            id: item.id,
            code: drugCode,
            name: drugName,
            spec: drugSpec,
            unit: drugUnit,
            price: drugPrice,
            deptId: item.deptId,
            mnemonicCode: drugMnemonic,
            drugsType: item.drugsType || item.drugsTypelD,
            manufacturer: drugManufacturer,
            drugsDosageID: item.drugsDosageID,
            // 保留原始数据用于调试
            _originalData: item
          };
        });

        console.log('处理后的药品列表:', this.drugList);
        console.log('药品列表长度:', this.drugList.length);
        console.log('第一个药品数据:', this.drugList[0]);

        // 设置加载完成标志
        this.drugsLoaded = true;

        // 强制触发 Vue 响应式更新
        this.$nextTick(() => {
          console.log('Vue nextTick 后的药品列表长度:', this.drugList.length);
          // 更新 selectKey 强制重新渲染下拉框
          this.selectKey++;
          this.$forceUpdate();
        });

        if (this.drugList.length === 0) {
          console.warn('药品列表为空，原始数据:', drugData);
          if (operation === '搜索药品') {
            this.$message.warning('未找到匹配的药品，请尝试其他关键词');
          } else {
            this.$message.info('数据库中暂无药品数据，请联系管理员添加药品信息');
            // 显示测试数据
            this.setDefaultDrugData();
          }
        } else {
          this.$message.success(`${operation}成功，找到 ${this.drugList.length} 种药品`);
        }
      } else {
        console.error(`${operation}失败，状态码:`, resp.status);
        this.$message.error(`${operation}失败，状态码: ${resp.status}`);
      }
    },

    // 设置默认药品数据（当API不可用时）- 匹配FmedItem格式
    setDefaultDrugData() {
      console.log('设置默认测试药品数据');
      this.drugList = [
        {
          id: 1,
          code: 'BLGKL',
          name: '板蓝根颗粒',
          spec: '10g*20袋',
          unit: '盒',
          price: 12.50,
          mnemonicCode: 'BLGKL',
          deptId: 1
        },
        {
          id: 2,
          code: 'GMQRKL',
          name: '感冒清热颗粒',
          spec: '12g*10袋',
          unit: '盒',
          price: 18.00,
          mnemonicCode: 'GMQRKL',
          deptId: 1
        },
        {
          id: 3,
          code: 'FFGCP',
          name: '复方甘草片',
          spec: '24片',
          unit: '盒',
          price: 8.50,
          mnemonicCode: 'FFGCP',
          deptId: 1
        },
        {
          id: 4,
          code: 'XCHKL',
          name: '小柴胡颗粒',
          spec: '10g*12袋',
          unit: '盒',
          price: 15.80,
          mnemonicCode: 'XCHKL',
          deptId: 1
        },
        {
          id: 5,
          code: 'HXZQS',
          name: '藿香正气水',
          spec: '10ml*10支',
          unit: '盒',
          price: 25.60,
          mnemonicCode: 'HXZQS',
          deptId: 1
        },
        {
          id: 6,
          code: 'AMXLJN',
          name: '阿莫西林胶囊',
          spec: '0.25g*24粒',
          unit: '盒',
          price: 16.80,
          mnemonicCode: 'AMXLJN',
          deptId: 1
        },
        {
          id: 7,
          code: 'BLFHSJN',
          name: '布洛芬缓释胶囊',
          spec: '0.3g*20粒',
          unit: '盒',
          price: 22.50,
          mnemonicCode: 'BLFHSJN',
          deptId: 1
        }
      ];
      this.$message.info('已加载测试药品数据，请检查药品API服务连接以获取真实数据');
    },

    // 添加新药品到数据库（可选功能）
    addNewDrug(drugData) {
      console.log('添加新药品:', drugData);
      this.loading = true;

      // 构建FmedItem对象
      const fmedItem = {
        itemName: drugData.name,
        mnemonicCode: drugData.mnemonicCode || drugData.code,
        spec: drugData.spec,
        unit: drugData.unit,
        price: drugData.price,
        deptId: drugData.deptId || 1
      };

      postRequest('/api/fmeditems', fmedItem).then(resp => {
        if (resp.status === 200) {
          this.$message.success('药品添加成功');
          // 重新加载药品列表
          this.loadInitialDrugs();
        } else {
          this.$message.error('药品添加失败');
        }
      }).catch(error => {
        console.error('添加药品错误:', error);
        this.$message.error('请求失败: ' + error.message);
      }).finally(() => {
        this.loading = false;
      });
    },

    // 根据药品ID获取默认药品信息
    getDefaultDrugById(drugId) {
      const defaultDrugs = [
        { id: 1, name: '阿莫西林胶囊', spec: '0.25g*24粒', unit: '盒', price: 12.50 },
        { id: 2, name: '布洛芬缓释胶囊', spec: '0.3g*20粒', unit: '盒', price: 18.00 },
        { id: 3, name: '维生素C片', spec: '0.1g*100片', unit: '瓶', price: 8.50 },
        { id: 4, name: '感冒灵颗粒', spec: '10g*12袋', unit: '盒', price: 15.80 },
        { id: 5, name: '头孢克肟胶囊', spec: '0.1g*12粒', unit: '盒', price: 25.60 },
        // 扩展更多药品ID
        { id: 9, name: '复方甘草片', spec: '24片', unit: '盒', price: 8.00 },
        { id: 10, name: '小儿感冒颗粒', spec: '10g*12袋', unit: '盒', price: 15.00 },
        { id: 29, name: '板蓝根颗粒', spec: '10g*20袋', unit: '盒', price: 12.00 },
        { id: 33, name: '999感冒灵', spec: '10g*9袋', unit: '盒', price: 18.50 },
        { id: 47, name: '维C银翘片', spec: '12片*2板', unit: '盒', price: 16.80 }
      ];

      return defaultDrugs.find(drug => drug.id == drugId);
    },

    // 从备用列表选择药品
    selectDrugFromList(drug) {
      console.log('=== 从列表选择药品开始 ===');
      console.log('选择的药品:', drug);
      console.log('药品ID:', drug.id);

      // 阻止重复选择
      if (this.selectedDrugId === drug.id) {
        console.log('重复选择同一药品，忽略');
        return;
      }

      this.selectedDrugId = drug.id;
      this.handleDrugSelect(drug);
      console.log('=== 从列表选择药品完成 ===');
    },

    // 根据ID选择药品（用于下拉框）
    handleDrugSelectById(drugId) {
      if (drugId) {
        const drug = this.drugList.find(item => item.id === drugId);
        if (drug) {
          this.handleDrugSelect(drug);
          // 不要直接关闭对话框，让用户填写用量和频次
        }
      }
    },

    // 处理药品选择
    handleDrugSelect(currentRow) {
      if (currentRow) {
        this.selectedDrug = {
          id: currentRow.id,
          code: currentRow.code,
          name: currentRow.name,
          spec: currentRow.spec,
          unit: currentRow.unit,
          price: currentRow.price || 0,
          usage: '',
          dosage: '',
          frequency: '',
          quantity: 1
        };

        console.log('选中药品:', this.selectedDrug);
        this.$message.success(`已选择药品：${currentRow.name}`);
      }
    },

    // 从处方中删除药品
    deleteDrugFromPrescription() {
      // 获取当前选中处方的状态
      const currentPrescription = this.tableData01.find(
        item => item.id === this.currentPrescriptionId
      );
      
      // 检查处方状态，只有暂存状态可删药
      if (!currentPrescription || currentPrescription.state !== '暂存') {
        this.$message.warning('只有暂存状态的处方可以删除药品');
        return;
      }

      // 检查是否选中了药品
      const selectedDrugs = this.$refs.singleTable.selection;
      if (!selectedDrugs || selectedDrugs.length === 0) {
        this.$message.warning('请先选择要删除的药品');
        return;
      }

      this.$confirm('确认删除选中的药品?', '提示', {
        type: 'warning'
      }).then(() => {
        this.loading = true;
        const selectedIds = selectedDrugs.map(drug => drug.id);

        console.log('删除药品IDs:', selectedIds);

        // 批量删除处方明细
        const deletePromises = selectedIds.map(id =>
          deleteRequest(`/prescription/detail/${id}`)
        );

        Promise.all(deletePromises).then(responses => {
          const successCount = responses.filter(resp => resp.status === 200).length;
          if (successCount === selectedIds.length) {
            this.$message.success(`成功删除 ${selectedDrugs.length} 种药品`);
            this.loadPrescriptionDrugs(this.currentPrescriptionId); // 刷新药品列表
          } else {
            this.$message.warning(`删除了 ${successCount}/${selectedIds.length} 种药品`);
            this.loadPrescriptionDrugs(this.currentPrescriptionId); // 刷新药品列表
          }
        }).catch(error => {
          console.error('删除药品错误:', error);
          this.$message.error('请求失败: ' + error.message);
        }).finally(() => {
          this.loading = false;
        });
      });
    },

    // 加载处方类型数据
    loadPrescriptionTypes() {
      console.log('加载处方类型数据...');

      // 设置处方类型选项
      this.selectData01 = [
        { value: 1, label: '草药' },
        { value: 2, label: '西药' },
        { value: 3, label: '检查' },
        { value: 4, label: '检验' },
        { value: 5, label: '处置' }
      ];

      // 设置频次选项
      this.selectData03 = [
        { value: '每日一次', label: '每日一次' },
        { value: '每日两次', label: '每日两次' },
        { value: '每日三次', label: '每日三次' },
        { value: '每日四次', label: '每日四次' },
        { value: '隔日一次', label: '隔日一次' },
        { value: '按需服用', label: '按需服用' }
      ];

      // 设置用法选项
      this.selectData04 = [
        { value: '口服', label: '口服' },
        { value: '外用', label: '外用' },
        { value: '静脉注射', label: '静脉注射' },
        { value: '肌肉注射', label: '肌肉注射' },
        { value: '皮下注射', label: '皮下注射' },
        { value: '雾化吸入', label: '雾化吸入' },
        { value: '滴眼', label: '滴眼' },
        { value: '滴鼻', label: '滴鼻' }
      ];

      // 设置默认选中第一个
      if (this.selectData01.length > 0) {
        this.select01 = this.selectData01[0].value;
      }
      if (this.selectData03.length > 0) {
        this.select03 = this.selectData03[0].value;
      }
      if (this.selectData04.length > 0) {
        this.select04 = this.selectData04[0].value;
      }

      console.log('处方类型数据加载完成:', {
        types: this.selectData01,
        frequencies: this.selectData03,
        usages: this.selectData04
      });
      return Promise.resolve(this.selectData01);
    },

    // 计算处方总金额 - 支持优化后的价格字段
    calculateTotalAmount() {
      if (!this.tableData08 || this.tableData08.length === 0) {
        return '0.00';
      }

      let total = 0;
      this.tableData08.forEach(item => {
        // 优先使用数字类型的价格字段，然后解析字符串价格
        let price = 0;
        if (typeof item.price === 'number') {
          price = item.price;
        } else if (item.danjia) {
          const priceStr = item.danjia.toString().replace(/[元￥]/g, '');
          price = parseFloat(priceStr) || 0;
        }

        // 解析数量，默认为1
        let quantity = 1;
        if (item.quantity) {
          quantity = parseFloat(item.quantity) || 1;
        } else if (item.yongliang) {
          // 尝试从用量中提取数字
          const quantityMatch = item.yongliang.match(/(\d+(?:\.\d+)?)/);
          if (quantityMatch) {
            quantity = parseFloat(quantityMatch[1]) || 1;
          }
        }

        // 考虑付数
        const doses = this.prescriptionForm.doses || 1;

        total += price * quantity * doses;
        console.log(`药品金额计算: ${item.mingcheng} - 单价:${price} × 数量:${quantity} × 付数:${doses} = ${price * quantity * doses}`);
      });

      console.log(`处方总金额: ${total.toFixed(2)}元`);
      return total.toFixed(2);
    },

    // 计算单个药品的小计 - 支持优化后的价格字段
    calculateItemTotal(item) {
      let price = 0;
      if (typeof item.price === 'number') {
        price = item.price;
      } else if (item.danjia) {
        const priceStr = item.danjia.toString().replace(/[元￥]/g, '');
        price = parseFloat(priceStr) || 0;
      }

      const quantity = item.quantity || 1;
      const doses = this.prescriptionForm.doses || 1;

      return (price * quantity * doses).toFixed(2);
    },

    // 更新药品数量
    updateDrugQuantity(item) {
      console.log('更新药品数量:', item.mingcheng, '数量:', item.quantity);

      if (!this.currentPrescriptionId || !item.id) {
        this.$forceUpdate(); // 强制更新视图以刷新总金额
        return;
      }

      // 构建更新数据
      const updateData = {
        drugId: item.drugId || item.id,
        drugName: item.mingcheng,
        dosage: item.yongliang,
        unit: item.danwei,
        usage: item.usage || '口服',
        frequency: item.frequency || '每日三次',
        quantity: item.quantity,
        price: parseFloat(item.danjia.replace(/[元￥]/g, '')) || 0,
        note: item.jiaozhu
      };

      // 调用更新API
      putRequest(`/prescription/${this.currentPrescriptionId}/drugs/${item.id}`, updateData).then(resp => {
        if (resp.status === 200) {
          console.log('药品数量更新成功');
        } else {
          console.error('药品数量更新失败');
        }
      }).catch(error => {
        console.error('更新药品数量错误:', error);
      });

      this.$forceUpdate(); // 强制更新视图以刷新总金额
    },

    // 根据药品ID获取药品详细信息
    async getDrugInfoById(drugsId) {
      try {
        console.log(`查询药品信息: DrugsID=${drugsId}`);

        // 1. 首先从当前药品列表中查找
        let drugInfo = this.drugList.find(drug => drug.id === drugsId);

        if (drugInfo) {
          console.log(`从本地药品列表找到: ${drugInfo.name}`);
          return drugInfo;
        }

        // 2. 如果本地没有，尝试加载更多药品数据
        console.log(`本地未找到，尝试加载更多药品数据查找 DrugsID=${drugsId}`);

        try {
          // 使用分页方式获取所有药品数据
          const maxPages = 20; // 最多查询20页
          const pageSize = 200;
          let allDrugs = [];

          for (let page = 1; page <= maxPages; page++) {
            try {
              const response = await getRequest(`/api/drugs/list?pageSize=${pageSize}&pn=${page}`);

              if (response && response.data && response.data.result && response.data.data) {
                const pageData = response.data.data;
                console.log(`第${page}页获取药品数量: ${pageData.length}`);

                if (pageData.length === 0) {
                  console.log(`第${page}页无数据，停止查询`);
                  break;
                }

                allDrugs = allDrugs.concat(pageData);

                // 检查是否找到目标药品
                const targetDrug = pageData.find(item => item.id === drugsId);
                if (targetDrug) {
                  console.log(`在第${page}页找到目标药品: ${targetDrug.drugsName || targetDrug.name}`);

                  drugInfo = {
                    id: targetDrug.id,
                    name: targetDrug.drugsName || targetDrug.name,
                    spec: targetDrug.drugsFormat || targetDrug.spec,
                    unit: targetDrug.drugsUnit || targetDrug.unit,
                    price: parseFloat(targetDrug.drugsPrice || targetDrug.price || 0),
                    code: targetDrug.drugsCode || targetDrug.code,
                    mnemonicCode: targetDrug.mnemonicCode
                  };

                  // 缓存所有获取到的药品
                  allDrugs.forEach(item => {
                    const processedDrug = {
                      id: item.id,
                      name: item.drugsName || item.name,
                      spec: item.drugsFormat || item.spec,
                      unit: item.drugsUnit || item.unit,
                      price: parseFloat(item.drugsPrice || item.price || 0),
                      code: item.drugsCode || item.code,
                      mnemonicCode: item.mnemonicCode
                    };

                    if (!this.drugList.find(existing => existing.id === processedDrug.id)) {
                      this.drugList.push(processedDrug);
                    }
                  });

                  console.log(`已缓存${allDrugs.length}条药品数据，本地总数量: ${this.drugList.length}`);
                  return drugInfo;
                }

                // 如果页面数据少于pageSize，说明是最后一页
                if (pageData.length < pageSize) {
                  console.log(`第${page}页数据不足${pageSize}条，已是最后一页`);
                  break;
                }
              } else {
                console.log(`第${page}页请求失败或无数据`);
                break;
              }
            } catch (pageError) {
              console.log(`第${page}页请求失败:`, pageError.message);
              break;
            }
          }

          // 缓存所有获取到的药品
          if (allDrugs.length > 0) {
            allDrugs.forEach(item => {
              const processedDrug = {
                id: item.id,
                name: item.drugsName || item.name,
                spec: item.drugsFormat || item.spec,
                unit: item.drugsUnit || item.unit,
                price: parseFloat(item.drugsPrice || item.price || 0),
                code: item.drugsCode || item.code,
                mnemonicCode: item.mnemonicCode
              };

              if (!this.drugList.find(existing => existing.id === processedDrug.id)) {
                this.drugList.push(processedDrug);
              }
            });

            console.log(`总共获取${allDrugs.length}条药品数据，本地总数量: ${this.drugList.length}`);

            // 最后再次查找目标药品
            drugInfo = this.drugList.find(drug => drug.id === drugsId);

            if (drugInfo) {
              console.log(`从完整列表中找到: ${drugInfo.name}`);
              return drugInfo;
            }
          }
        } catch (apiError) {
          console.log(`加载药品列表失败:`, apiError.message);
        }

        // 如果还是找不到，使用备用的药品信息映射
        console.log(`未找到药品信息，尝试备用映射，DrugsID=${drugsId}`);

        // 常见的历史药品ID映射（可以根据实际情况扩展）
        const fallbackDrugMap = {
          10: { name: '阿莫西林胶囊', spec: '0.25g*24粒', unit: '盒', price: 15.5 },
          29: { name: '头孢克肟胶囊', spec: '0.1g*12粒', unit: '盒', price: 25.8 },
          // 可以根据实际需要添加更多映射
        };

        if (fallbackDrugMap[drugsId]) {
          const fallbackInfo = fallbackDrugMap[drugsId];
          drugInfo = {
            id: drugsId,
            name: fallbackInfo.name,
            spec: fallbackInfo.spec,
            unit: fallbackInfo.unit,
            price: fallbackInfo.price,
            code: '',
            mnemonicCode: '',
            _isFallback: true // 标记为备用数据
          };

          console.log(`使用备用药品信息: ${drugInfo.name}`);

          // 缓存备用信息
          this.drugList.push(drugInfo);
          return drugInfo;
        }

        console.log(`未找到药品信息，DrugsID=${drugsId}`);
        return null;

      } catch (error) {
        console.error('获取药品信息失败:', error);
        return null;
      }
    },

    // 测试药品列表API
    async testDrugListAPI() {
      console.log('=== 测试药品列表API ===');
      try {
        const response = await getRequest('/api/drugs/list?pageSize=10');
        console.log('药品列表API响应:', response);
        if (response && response.data) {
          console.log('✅ 药品列表API可用');
          return true;
        }
      } catch (error) {
        console.log('❌ 药品列表API失败:', error.message);
      }
      console.log('=== 药品列表API测试完成 ===');
      return false;
    },

    // 删除药品
    removeDrug(index) {
      this.$confirm('确认删除该药品?', '提示', {
        type: 'warning'
      }).then(() => {
        this.tableData08.splice(index, 1);
        this.$message.success('药品删除成功');
        this.$forceUpdate(); // 强制更新界面以重新计算总金额
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 手动刷新处方药品数据
    refreshPrescriptionDrugs() {
      if (this.currentPrescriptionId) {
        console.log('手动刷新处方药品数据');
        this.loadPrescriptionDrugs(this.currentPrescriptionId);
      } else {
        this.$message.warning('请先选择一个处方');
      }
    },

    // 加载当前处方的药品数据
    async loadPrescriptionDrugs(prescriptionId) {
      if (!prescriptionId) {
        this.tableData08 = [];
        return;
      }

      console.log('加载处方药品数据，处方ID:', prescriptionId);
      this.loading = true;

      try {
        const resp = await getRequest(`/prescription/detail/${prescriptionId}`);
        if (resp.status === 200) {
          console.log('处方药品数据:', resp.data);

          // 处理处方详情数据格式
          let prescriptionDetail = null;
          if (resp.data && resp.data.data) {
            prescriptionDetail = resp.data.data;
          } else if (resp.data) {
            prescriptionDetail = resp.data;
          }

          // 从处方详情中提取药品数据
          let drugData = [];
          if (prescriptionDetail) {
            // 如果处方详情包含药品列表
            if (prescriptionDetail.details && Array.isArray(prescriptionDetail.details)) {
              drugData = prescriptionDetail.details;
            } else if (prescriptionDetail.prescriptionDetails && Array.isArray(prescriptionDetail.prescriptionDetails)) {
              drugData = prescriptionDetail.prescriptionDetails;
            } else if (prescriptionDetail.items && Array.isArray(prescriptionDetail.items)) {
              drugData = prescriptionDetail.items;
            }
          }

          // 转换数据格式以适配前端显示 - 支持异步药品信息查询
          this.tableData08 = await Promise.all(drugData.map(async (item, index) => {
            console.log(`处理处方药品数据项[${index}]:`, item);

            // 支持实际数据库表字段映射：
            // 1. prescriptiondetailed表字段 (DrugsID, DrugsUsage, Dosage, Frequency, Amount, State)
            // 2. 通过DrugsID异步查询drugs表获取药品详细信息
            // 3. 兼容旧字段 (mingcheng, yongliang, danwei, danjia)

            // 直接使用数据库字段名（注意大小写）
            const drugsId = item.DrugsID || item.drugsId || item.drugsID;
            console.log(`提取DrugsID: ${drugsId}, 原始数据:`, item);

            // 异步获取药品详细信息
            let drugInfo = null;
            if (drugsId) {
              console.log(`开始查询药品信息，DrugsID: ${drugsId}`);
              drugInfo = await this.getDrugInfoById(drugsId);
              console.log(`查询结果:`, drugInfo);
            } else {
              console.warn('DrugsID为空，无法查询药品信息');
            }

            // 优先使用查询到的药品详细信息
            const drugName = drugInfo ? drugInfo.name : `药品ID:${drugsId || item.ID}`;
            const drugSpec = drugInfo ? drugInfo.spec : '';
            const drugUnit = drugInfo ? drugInfo.unit : '个';
            const drugPrice = drugInfo ? parseFloat(drugInfo.price) : 0;

            // 处方相关信息 - 直接使用数据库字段名
            const dosage = item.Dosage || item.dosage || item.yongliang || '按医嘱';
            const frequency = item.Frequency || item.frequency || '';
            const usage = item.DrugsUsage || item.drugsUsage || item.usage || item.yongfa || '';
            const amount = parseFloat(item.Amount || item.amount || 0);
            const quantity = drugPrice > 0 ? Math.round(amount / drugPrice) || 1 : 1; // 通过金额和单价计算数量
            const note = item.note || item.jiaozhu || '';

            console.log(`处方药品映射: DrugsID=${drugsId}, 找到药品=${!!drugInfo}, 名称=${drugName}, 规格=${drugSpec}, 单位=${drugUnit}, 价格=${drugPrice}, 用量=${dosage}, 用法=${usage}, 频次=${frequency}`);

            return {
              id: item.id || item.ID,
              mingcheng: drugName,
              yongliang: dosage,
              danwei: drugUnit,
              jiaozhu: note,
              danjia: drugPrice > 0 ? `${drugPrice}元` : '0元',
              quantity: quantity,
              usage: usage || frequency,
              frequency: frequency,
              // 新增字段支持
              spec: drugSpec,
              price: drugPrice, // 确保价格是数字类型
              // 保留原始数据用于调试
              _originalData: item
            };
          }));

          console.log('处理后的药品数据:', this.tableData08);

          // 详细检查每个药品的渲染数据
          this.tableData08.forEach((item, index) => {
            console.log(`药品${index + 1}渲染数据:`, {
              mingcheng: item.mingcheng,
              yongliang: item.yongliang,
              danwei: item.danwei,
              danjia: item.danjia,
              price: item.price,
              quantity: item.quantity
            });
          });

          if (this.tableData08.length > 0) {
            this.$message.success(`成功加载 ${this.tableData08.length} 种药品`);
          } else {
            this.$message.info('当前处方暂无药品');
          }
        } else {
          this.$message.error('加载处方药品失败');
          this.tableData08 = [];
        }
      } catch (error) {
        console.error('加载处方药品错误:', error);
        this.$message.error('请求失败: ' + error.message);
        // 如果API不存在，使用默认数据
        this.setDefaultPrescriptionDrugData();
      } finally {
        this.loading = false;
      }
    },

    // 设置默认处方药品数据（当API不可用时）
    setDefaultPrescriptionDrugData() {
      this.tableData08 = [
        {
          id: 1,
          mingcheng: '三七',
          yongliang: '一次5克',
          danwei: '克',
          jiaozhu: '研粉末吞服',
          danjia: '2元',
          quantity: 1
        },
        {
          id: 2,
          mingcheng: '肉桂',
          yongliang: '一次2克',
          danwei: '克',
          jiaozhu: '后下',
          danjia: '0.5元',
          quantity: 1
        },
        {
          id: 3,
          mingcheng: '五灵脂',
          yongliang: '一次1克',
          danwei: '克',
          jiaozhu: '包煎',
          danjia: '1元',
          quantity: 1
        },
        {
          id: 4,
          mingcheng: '阿胶',
          yongliang: '一次2克',
          danwei: '克',
          jiaozhu: '冲服',
          danjia: '1.5元',
          quantity: 1
        }
      ];
    }
  },
  created() {
    // 组件创建时加载初始数据
    this.loadPrescriptions().catch(() => {});
    this.loadTemplates().catch(() => {});
    this.loadPrescriptionTypes().catch(() => {});
  },

  beforeUnmount() {
    // 组件卸载时取消所有未完成的请求
    if (this.currentRequest) {
      this.currentRequest.cancel();
    }
  },
  watch: {
    radio01() {
      this.loadTemplates();
    }
  },
  data() {
    return {
      loading: false,
      currentPrescriptionId: null, // 当前选中的处方ID
      currentTemplateId: null, // 当前选中的模板ID
      dialog01Visible: false,
      dialog02Visible: false,
      dialog03Visible: false,
      dialog04Visible: false, // 模板选择对话框
      dialog05Visible: false, // 模板明细对话框
      dialogRadio01: '1',
      templateList: [], // 处方模板列表
      drugSearchKey: '', // 药品搜索关键词
      drugList: [], // 药品列表
      selectedDrugId: null, // 选中的药品ID
      selectKey: 0, // 下拉框强制刷新key
      drugsLoaded: false, // 药品是否已加载标志
      drugInfoCache: {}, // 药品信息缓存
      tableData03: [], // 已选择药品列表
      currentTemplateDetails: [], // 当前模板明细

      // 处方表单数据
      prescriptionForm: {
        doses: 1, // 付数
        treatment: '', // 治法
        treatmentDetail: '', // 治法详细
        advice: '' // 医嘱
      },
      selectedDrug: { // 选中的药品信息
        id: null,
        code: '',
        name: '',
        spec: '',
        unit: '',
        usage: '',
        dosage: '',
        frequency: '',
        quantity: 1
      },
      usageOptions: [ // 用法选项
        { value: '口服', label: '口服' },
        { value: '外用', label: '外用' },
        { value: '静脉注射', label: '静脉注射' },
        { value: '肌肉注射', label: '肌肉注射' }
      ],
      frequencyOptions: [ // 频次选项
        { value: '每日一次', label: '每日一次' },
        { value: '每日两次', label: '每日两次' },
        { value: '每日三次', label: '每日三次' },
        { value: '每日四次', label: '每日四次' },
        { value: '隔日一次', label: '隔日一次' },
        { value: '每周一次', label: '每周一次' }
      ],
      selectData01: [],
      selectData02: [],
      selectData03: [],
      selectData04: [],
      select01: [],
      select02: [],
      select03: [],
      select04: [],
      tableData01: [],
      templateName: '',
      currentPrescriptionName: '', // 当前处方名称
      tableData06: [{
        name: '病毒性感冒处方',
      }, {
        name: '寒湿瘀郁证处方',
      }, {
        name: '细菌炎症处方',
      }],
      tableData08: [], // 当前处方的药品数据，动态加载
      tableData07: [{
        datetime: '2018-10-11 10:02:23',
        name01: '病毒性感冒',
        name02: '新建处方03'
      }, {
        datetime: '2018-10-11 10:00:03',
        name01: '病毒性感冒',
        name02: '新建处方02'
      }, {
        datetime: '2018-10-11 09:52:23',
        name01: '病毒性感冒',
        name02: '新建处方01'
      }, {
        datetime: '2018-10-11 09:42:23',
        name01: '咽炎',
        name02: '新建处方02'
      }, {
        datetime: '2018-10-11 09:40:23',
        name01: '咽炎',
        name02: '新建处方01'
      }],
      radio01: '3',
      activeName: 'first',
      tempPrescriptionName: '', // 临时存储处方名称输入
    }
  }
}
</script>

<style></style>