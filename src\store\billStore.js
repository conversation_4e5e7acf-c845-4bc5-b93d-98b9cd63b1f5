// 全局费用管理Store
import { reactive } from 'vue'

// 全局费用状态
export const billStore = reactive({
  // 当前患者信息
  currentPatient: {
    registId: null,
    patientId: null,
    name: '',
    gender: '',
    age: ''
  },

  // 费用项目列表
  billItems: [],

  // 费用统计
  totalAmount: 0,

  // 添加费用项目
  addBillItem(item) {
    console.log('添加费用项目:', item);
    
    // 检查是否已存在相同项目
    const existingIndex = this.billItems.findIndex(
      bill => bill.type === item.type && 
              bill.sourceId === item.sourceId &&
              bill.mingcheng === item.mingcheng
    );

    if (existingIndex >= 0) {
      // 更新现有项目
      this.billItems[existingIndex] = { ...this.billItems[existingIndex], ...item };
      console.log('更新现有费用项目');
    } else {
      // 添加新项目
      this.billItems.push({
        id: Date.now(), // 临时ID
        ...item,
        createTime: new Date().toLocaleString()
      });
      console.log('添加新费用项目');
    }

    this.calculateTotal();
    this.notifyBillUpdate();
  },

  // 移除费用项目
  removeBillItem(itemId) {
    const index = this.billItems.findIndex(item => item.id === itemId);
    if (index >= 0) {
      this.billItems.splice(index, 1);
      this.calculateTotal();
      this.notifyBillUpdate();
      console.log('移除费用项目:', itemId);
    }
  },

  // 更新费用项目状态
  updateBillItemStatus(itemId, status) {
    const item = this.billItems.find(item => item.id === itemId);
    if (item) {
      item.zhuangtai = status;
      this.notifyBillUpdate();
      console.log('更新费用项目状态:', itemId, status);
    }
  },

  // 设置当前患者
  setCurrentPatient(patientInfo) {
    this.currentPatient = { ...this.currentPatient, ...patientInfo };
    console.log('设置当前患者:', this.currentPatient);
  },

  // 清空费用列表
  clearBillItems() {
    this.billItems = [];
    this.totalAmount = 0;
    console.log('清空费用列表');
  },

  // 计算总金额
  calculateTotal() {
    this.totalAmount = this.billItems.reduce((total, item) => {
      const amount = parseFloat(item.jiner) || 0;
      return total + amount;
    }, 0);
  },

  // 通知费用更新（可以用于触发其他组件更新）
  notifyBillUpdate() {
    // 可以在这里添加事件总线或其他通知机制
    window.dispatchEvent(new CustomEvent('billUpdated', {
      detail: {
        items: this.billItems,
        total: this.totalAmount,
        patient: this.currentPatient
      }
    }));
  },

  // 获取指定类型的费用项目
  getBillItemsByType(type) {
    return this.billItems.filter(item => item.type === type);
  },

  // 获取指定状态的费用项目
  getBillItemsByStatus(status) {
    return this.billItems.filter(item => item.zhuangtai === status);
  }
});

// 费用项目工厂函数
export const createBillItem = {
  // 创建检查费用项目
  checkItem(checkData) {
    return {
      type: 'check',
      sourceId: checkData.id,
      mingcheng: checkData.name || checkData.itemName || '检查项目',
      guige: checkData.specification || checkData.spec || '',
      shuliang: '1',
      fushu: '1',
      danjia: (checkData.price || checkData.fee || 0).toFixed(2),
      jiner: (checkData.price || checkData.fee || 0).toFixed(2),
      zhuangtai: '已申请',
      sourceData: checkData
    };
  },

  // 创建检验费用项目
  labItem(labData) {
    return {
      type: 'lab',
      sourceId: labData.id,
      mingcheng: labData.name || labData.itemName || '检验项目',
      guige: labData.specification || labData.spec || '',
      shuliang: '1',
      fushu: '1',
      danjia: (labData.price || labData.fee || 0).toFixed(2),
      jiner: (labData.price || labData.fee || 0).toFixed(2),
      zhuangtai: '已申请',
      sourceData: labData
    };
  },

  // 创建处置费用项目
  treatmentItem(treatmentData) {
    return {
      type: 'treatment',
      sourceId: treatmentData.id,
      mingcheng: treatmentData.name || treatmentData.itemName || '处置项目',
      guige: treatmentData.specification || treatmentData.spec || '',
      shuliang: treatmentData.quantity || '1',
      fushu: '1',
      danjia: (treatmentData.price || treatmentData.fee || 0).toFixed(2),
      jiner: ((treatmentData.price || treatmentData.fee || 0) * (treatmentData.quantity || 1)).toFixed(2),
      zhuangtai: '已申请',
      sourceData: treatmentData
    };
  },

  // 创建处方费用项目
  prescriptionItem(drugData, prescriptionInfo) {
    return {
      type: 'prescription',
      sourceId: `${prescriptionInfo.id}_${drugData.drugsId}`,
      mingcheng: drugData.drugName || `药品ID:${drugData.drugsId}`,
      guige: drugData.specification || '',
      shuliang: drugData.amount || '1',
      fushu: drugData.amount || '1',
      danjia: (drugData.price || 0).toFixed(2),
      jiner: ((drugData.price || 0) * (drugData.amount || 1)).toFixed(2),
      zhuangtai: '已发送',
      sourceData: { drug: drugData, prescription: prescriptionInfo }
    };
  },

  // 创建挂号费用项目
  registrationItem(registData) {
    return {
      type: 'registration',
      sourceId: registData.id,
      mingcheng: '挂号费',
      guige: registData.deptName || '',
      shuliang: '1',
      fushu: '1',
      danjia: (registData.registFee || 5).toFixed(2),
      jiner: (registData.registFee || 5).toFixed(2),
      zhuangtai: '已收费',
      sourceData: registData
    };
  }
};

// 导出默认对象
export default billStore;
