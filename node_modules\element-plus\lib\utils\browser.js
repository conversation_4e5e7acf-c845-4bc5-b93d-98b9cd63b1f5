'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var core = require('@vueuse/core');

const isFirefox = () => core.isClient && /firefox/i.test(window.navigator.userAgent);

Object.defineProperty(exports, 'isClient', {
  enumerable: true,
  get: function () { return core.isClient; }
});
Object.defineProperty(exports, 'isIOS', {
  enumerable: true,
  get: function () { return core.isIOS; }
});
exports.isFirefox = isFirefox;
//# sourceMappingURL=browser.js.map
