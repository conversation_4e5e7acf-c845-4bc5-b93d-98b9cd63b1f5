{"version": 3, "file": "render-helper.js", "sources": ["../../../../../../../packages/components/table/src/table-column/render-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport {\n  Comment,\n  computed,\n  getCurrentInstance,\n  h,\n  ref,\n  renderSlot,\n  unref,\n  watchEffect,\n} from 'vue'\nimport { debugWarn, isArray, isUndefined } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  cellForced,\n  defaultRenderCell,\n  getDefaultClassName,\n  treeCellPrefix,\n} from '../config'\nimport { parseMinWidth, parseWidth } from '../util'\nimport type { ComputedRef } from 'vue'\nimport type { TableColumn, TableColumnCtx } from './defaults'\n\nfunction useRender<T>(\n  props: TableColumnCtx<T>,\n  slots,\n  owner: ComputedRef<any>\n) {\n  const instance = getCurrentInstance() as TableColumn<T>\n  const columnId = ref('')\n  const isSubColumn = ref(false)\n  const realAlign = ref<string>()\n  const realHeaderAlign = ref<string>()\n  const ns = useNamespace('table')\n  watchEffect(() => {\n    realAlign.value = props.align ? `is-${props.align}` : null\n    // nextline help render\n    realAlign.value\n  })\n  watchEffect(() => {\n    realHeaderAlign.value = props.headerAlign\n      ? `is-${props.headerAlign}`\n      : realAlign.value\n    // nextline help render\n    realHeaderAlign.value\n  })\n  const columnOrTableParent = computed(() => {\n    let parent: any = instance.vnode.vParent || instance.parent\n    while (parent && !parent.tableId && !parent.columnId) {\n      parent = parent.vnode.vParent || parent.parent\n    }\n    return parent\n  })\n  const hasTreeColumn = computed<boolean>(() => {\n    const { store } = instance.parent\n    if (!store) return false\n    const { treeData } = store.states\n    const treeDataValue = treeData.value\n    return treeDataValue && Object.keys(treeDataValue).length > 0\n  })\n\n  const realWidth = ref(parseWidth(props.width))\n  const realMinWidth = ref(parseMinWidth(props.minWidth))\n  const setColumnWidth = (column: TableColumnCtx<T>) => {\n    if (realWidth.value) column.width = realWidth.value\n    if (realMinWidth.value) {\n      column.minWidth = realMinWidth.value\n    }\n    if (!realWidth.value && realMinWidth.value) {\n      column.width = undefined\n    }\n    if (!column.minWidth) {\n      column.minWidth = 80\n    }\n    column.realWidth = Number(\n      isUndefined(column.width) ? column.minWidth : column.width\n    )\n    return column\n  }\n  const setColumnForcedProps = (column: TableColumnCtx<T>) => {\n    // 对于特定类型的 column，某些属性不允许设置\n    const type = column.type\n    const source = cellForced[type] || {}\n    Object.keys(source).forEach((prop) => {\n      const value = source[prop]\n      if (prop !== 'className' && !isUndefined(value)) {\n        column[prop] = value\n      }\n    })\n    const className = getDefaultClassName(type)\n    if (className) {\n      const forceClass = `${unref(ns.namespace)}-${className}`\n      column.className = column.className\n        ? `${column.className} ${forceClass}`\n        : forceClass\n    }\n    return column\n  }\n\n  const checkSubColumn = (children: TableColumn<T> | TableColumn<T>[]) => {\n    if (isArray(children)) {\n      children.forEach((child) => check(child))\n    } else {\n      check(children)\n    }\n    function check(item: TableColumn<T>) {\n      if (item?.type?.name === 'ElTableColumn') {\n        item.vParent = instance\n      }\n    }\n  }\n  const setColumnRenders = (column: TableColumnCtx<T>) => {\n    // renderHeader 属性不推荐使用。\n    if (props.renderHeader) {\n      debugWarn(\n        'TableColumn',\n        'Comparing to render-header, scoped-slot header is easier to use. We recommend users to use scoped-slot header.'\n      )\n    } else if (column.type !== 'selection') {\n      column.renderHeader = (scope) => {\n        // help render\n        instance.columnConfig.value['label']\n        return renderSlot(slots, 'header', scope, () => [column.label])\n      }\n    }\n\n    if (slots['filter-icon']) {\n      column.renderFilterIcon = (scope) => {\n        return renderSlot(slots, 'filter-icon', scope)\n      }\n    }\n\n    if (slots.expand) {\n      column.renderExpand = (scope) => {\n        return renderSlot(slots, 'expand', scope)\n      }\n    }\n\n    let originRenderCell = column.renderCell\n    // TODO: 这里的实现调整\n    if (column.type === 'expand') {\n      // 对于展开行，renderCell 不允许配置的。在上一步中已经设置过，这里需要简单封装一下。\n      column.renderCell = (data) =>\n        h(\n          'div',\n          {\n            class: 'cell',\n          },\n          [originRenderCell(data)]\n        )\n      owner.value.renderExpanded = (data) => {\n        return slots.default ? slots.default(data) : slots.default\n      }\n    } else {\n      originRenderCell = originRenderCell || defaultRenderCell\n      // 对 renderCell 进行包装\n      column.renderCell = (data) => {\n        let children = null\n        if (slots.default) {\n          const vnodes = slots.default(data)\n          children = vnodes.some((v) => v.type !== Comment)\n            ? vnodes\n            : originRenderCell(data)\n        } else {\n          children = originRenderCell(data)\n        }\n\n        const { columns } = owner.value.store.states\n        const firstUserColumnIndex = columns.value.findIndex(\n          (item) => item.type === 'default'\n        )\n        const shouldCreatePlaceholder =\n          hasTreeColumn.value && data.cellIndex === firstUserColumnIndex\n        const prefix = treeCellPrefix(data, shouldCreatePlaceholder)\n        const props = {\n          class: 'cell',\n          style: {},\n        }\n        if (column.showOverflowTooltip) {\n          props.class = `${props.class} ${unref(ns.namespace)}-tooltip`\n          props.style = {\n            width: `${\n              (data.column.realWidth || Number(data.column.width)) - 1\n            }px`,\n          }\n        }\n        checkSubColumn(children)\n        return h('div', props, [prefix, children])\n      }\n    }\n    return column\n  }\n  const getPropsData = (...propsKey: unknown[]) => {\n    return propsKey.reduce((prev, cur) => {\n      if (isArray(cur)) {\n        cur.forEach((key) => {\n          prev[key] = props[key]\n        })\n      }\n      return prev\n    }, {})\n  }\n  const getColumnElIndex = (children, child) => {\n    return Array.prototype.indexOf.call(children, child)\n  }\n\n  const updateColumnOrder = () => {\n    owner.value.store.commit('updateColumnOrder', instance.columnConfig.value)\n  }\n\n  return {\n    columnId,\n    realAlign,\n    isSubColumn,\n    realHeaderAlign,\n    columnOrTableParent,\n    setColumnWidth,\n    setColumnForcedProps,\n    setColumnRenders,\n    getPropsData,\n    getColumnElIndex,\n    updateColumnOrder,\n  }\n}\n\nexport default useRender\n"], "names": ["getCurrentInstance", "ref", "useNamespace", "watchEffect", "computed", "parse<PERSON>idth", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "isUndefined", "cellForced", "getDefaultClassName", "unref", "isArray", "debugWarn", "renderSlot", "h", "defaultRenderCell", "Comment", "treeCellPrefix"], "mappings": ";;;;;;;;;;;;AAmBA,SAAS,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AACxC,EAAE,MAAM,QAAQ,GAAGA,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,QAAQ,GAAGC,OAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,EAAE,MAAM,WAAW,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACjC,EAAE,MAAM,SAAS,GAAGA,OAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,eAAe,GAAGA,OAAG,EAAE,CAAC;AAChC,EAAE,MAAM,EAAE,GAAGC,kBAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAEC,eAAW,CAAC,MAAM;AACpB,IAAI,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/D,IAAI,SAAS,CAAC,KAAK,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAEA,eAAW,CAAC,MAAM;AACpB,IAAI,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;AAC5F,IAAI,eAAe,CAAC,KAAK,CAAC;AAC1B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAGC,YAAQ,CAAC,MAAM;AAC7C,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC;AAC3D,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC;AACrD,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAGA,YAAQ,CAAC,MAAM;AACvC,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;AACtC,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;AACtC,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;AACzC,IAAI,OAAO,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAGH,OAAG,CAACI,eAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,EAAE,MAAM,YAAY,GAAGJ,OAAG,CAACK,kBAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1D,EAAE,MAAM,cAAc,GAAG,CAAC,MAAM,KAAK;AACrC,IAAI,IAAI,SAAS,CAAC,KAAK;AACvB,MAAM,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AACrC,IAAI,IAAI,YAAY,CAAC,KAAK,EAAE;AAC5B,MAAM,MAAM,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,EAAE;AAChD,MAAM,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1B,MAAM,MAAM,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC3B,KAAK;AACL,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,CAACC,iBAAW,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1F,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,MAAM,KAAK;AAC3C,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC7B,IAAI,MAAM,MAAM,GAAGC,iBAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAC1C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC1C,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,MAAM,IAAI,IAAI,KAAK,WAAW,IAAI,CAACD,iBAAW,CAAC,KAAK,CAAC,EAAE;AACvD,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC7B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,SAAS,GAAGE,0BAAmB,CAAC,IAAI,CAAC,CAAC;AAChD,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,UAAU,GAAG,CAAC,EAAEC,SAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC;AAC7F,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,QAAQ,KAAK;AACvC,IAAI,IAAIC,cAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAChD,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,SAAS,KAAK,CAAC,IAAI,EAAE;AACzB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM,eAAe,EAAE;AACrG,QAAQ,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;AAChC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,MAAM,KAAK;AACvC,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5B,MAAMC,eAAS,CAAC,aAAa,EAAE,gHAAgH,CAAC,CAAC;AACjJ,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;AAC5C,MAAM,MAAM,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACvC,QAAQ,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAQ,OAAOC,cAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,aAAa,CAAC,EAAE;AAC9B,MAAM,MAAM,CAAC,gBAAgB,GAAG,CAAC,KAAK,KAAK;AAC3C,QAAQ,OAAOA,cAAU,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACvD,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,MAAM,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACvC,QAAQ,OAAOA,cAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClD,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC;AAC7C,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,KAAKC,KAAC,CAAC,KAAK,EAAE;AAC7C,QAAQ,KAAK,EAAE,MAAM;AACrB,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnC,MAAM,KAAK,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,IAAI,KAAK;AAC7C,QAAQ,OAAO,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;AACnE,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,gBAAgB,GAAG,gBAAgB,IAAIC,wBAAiB,CAAC;AAC/D,MAAM,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,KAAK;AACpC,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC5B,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7C,UAAU,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAKC,WAAO,CAAC,GAAG,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC9F,SAAS,MAAM;AACf,UAAU,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACrD,QAAQ,MAAM,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;AAChG,QAAQ,MAAM,uBAAuB,GAAG,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,oBAAoB,CAAC;AACvG,QAAQ,MAAM,MAAM,GAAGC,qBAAc,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;AACrE,QAAQ,MAAM,MAAM,GAAG;AACvB,UAAU,KAAK,EAAE,MAAM;AACvB,UAAU,KAAK,EAAE,EAAE;AACnB,SAAS,CAAC;AACV,QAAQ,IAAI,MAAM,CAAC,mBAAmB,EAAE;AACxC,UAAU,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAEP,SAAK,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC1E,UAAU,MAAM,CAAC,KAAK,GAAG;AACzB,YAAY,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;AAClF,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjC,QAAQ,OAAOI,KAAC,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;AACpD,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,GAAG,QAAQ,KAAK;AACxC,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK;AAC1C,MAAM,IAAIH,cAAO,CAAC,GAAG,CAAC,EAAE;AACxB,QAAQ,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC7B,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACjC,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,KAAK,KAAK;AAChD,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACzD,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,MAAM;AAClC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC/E,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,cAAc;AAClB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,GAAG,CAAC;AACJ;;;;"}