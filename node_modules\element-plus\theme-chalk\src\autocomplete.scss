@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;

@include b(autocomplete) {
  @include set-component-css-var('input', $input);
}

@include b(autocomplete) {
  width: getCssVar('input-width');
  position: relative;
  display: inline-block;

  @include e(popper) {
    @include picker-popper(
      getCssVar('bg-color', 'overlay'),
      1px solid getCssVar('border-color', 'light'),
      getCssVar('box-shadow', 'light')
    );
  }
}

@include b(autocomplete-suggestion) {
  border-radius: getCssVar('border-radius', 'base');
  box-sizing: border-box;

  @include e(wrap) {
    max-height: 280px;
    padding: 10px 0;
    box-sizing: border-box;
  }

  @include e(list) {
    margin: 0;
    padding: 0;
  }

  & li {
    padding: 0 20px;
    margin: 0;
    line-height: 34px;
    cursor: pointer;
    color: getCssVar('text-color', 'regular');
    font-size: getCssVar('font-size', 'base');
    list-style: none;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: map.get($select-option, 'hover-background');
    }

    &.highlighted {
      background-color: map.get($select-option, 'hover-background');
    }

    &.divider {
      margin-top: 6px;
      border-top: 1px solid getCssVar('color', 'black');
    }

    &.divider:last-child {
      margin-bottom: -6px;
    }
  }

  @include when(loading) {
    li {
      text-align: center;
      height: 100px;
      line-height: 100px;
      font-size: 20px;
      color: getCssVar('text-color', 'secondary');
      @include utils-vertical-center;

      &:hover {
        background-color: getCssVar('bg-color', 'overlay');
      }
    }

    & .#{$namespace}-icon-loading {
      vertical-align: middle;
    }
  }
}
