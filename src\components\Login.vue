<template>
  <div class="login-container">
    <!-- 背景图容器 -->
    <div class="background">
      <img src="public\image\背景.jpg" alt="医院背景" />
    </div>

    <!-- 左右容器（实现等高对齐） -->
    <div class="row">
      <!-- 左侧半透明蒙层 + 文案 -->
      <div class="col left-col">
        <div class="overlay">
          <div class="text-group">
            <p class="welcome">欢迎~</p>
            <p class="system-name">云医院HIS系统</p>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="col right-col">
        <div class="form-card">
          <div class="card-header">
            <h2>云医院HIS系统</h2>
          </div>
          <el-form 
            :model="form" 
            :rules="rules" 
            ref="loginForm"
          >
            <el-form-item prop="userName">
              <el-input 
                v-model="form.userName" 
                placeholder="请输入工号" 
                prefix-icon="el-icon-user"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input 
                v-model="form.password" 
                type="password" 
                placeholder="请输入密码" 
                prefix-icon="el-icon-lock"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                class="login-btn" 
                @click="submitClick"
              >
                登录系统
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { postReq } from '../utils/api';
import { useUserStore } from '../store/user.js';

const router = useRouter();
const userStore = useUserStore();
const form = ref({
  userName: 'admin',
  password: 'admin123'
});

const rules = ref({
  userName: [
    { required: true, message: '请输入工号', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3-20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 16, message: '长度在 6-16 个字符', trigger: 'blur' }
  ]
});

// 修正：使用 form.value 而非 loginForm.value
const submitClick = () => {
  console.log('提交数据:', form.value); // 调试：确认数据
  postReq("/user/login", form.value).then(resp => {
    console.log('登录响应:', resp); // 调试：确认响应
    if (resp.data.result) {
      let u = resp.data.data;
      u.token = resp.data.token;
      u.isAuth = true;
      if (!u.id) {
        console.warn('登录成功但未返回用户ID，使用默认值');
        u.id = '000000';
      }
      userStore.setAuthenticated(u);
      router.push('/home');
    } else {
      ElMessageBox.alert(resp.data.errMsg, '提示', {});
    }
  }).catch(err => {
    console.error('登录请求失败:', err); // 错误处理
  });
};
</script>

<style scoped>
/* 页面基础布局 */
.login-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 背景图样式（全屏覆盖） */
.background {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 0;
}

.background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 左右等高容器 */
.row {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.col {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 左侧列（半透明蒙层） */
.left-col {
  width: 320px; /* 与表单宽度一致 */
  height: 320px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}

.overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-group {
  text-align: center;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.welcome {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.system-name {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* 右侧列（登录表单） */
.right-col {
  width: 320px; /* 与表单宽度一致 */
}

/* 表单卡片样式 */
.form-card {
  width: 100%; /* 占满右侧列 */
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  box-sizing: border-box;
}

.card-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.card-header h2 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
}

/* 输入框样式 */
.el-input {
  --el-input-border-color: #e5e5e5;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.el-input:focus {
  --el-input-border-color: #66b1ff;
  box-shadow: 0 0 8px rgba(102, 177, 255, 0.2);
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  background: linear-gradient(90deg, #66b1ff, #99d1ff);
  border: none;
  border-radius: 8px;
  padding: 0.6rem 0;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(102, 177, 255, 0.2);
}

.login-btn:active {
  transform: translateY(1px);
  box-shadow: none;
}
</style>